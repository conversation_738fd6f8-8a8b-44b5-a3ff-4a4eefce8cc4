import json
import uuid
import time
import os
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from threading import Lock
from common.logger.logger import logger
from .es_storage import MetricsESStorage


@dataclass
class TaskMetrics:
    """任务级别的指标数据"""
    task_id: str
    session_id: str
    user_id: str
    task_type: str  # question_answer, code_execution
    task_instruction: str
    start_time: str
    end_time: Optional[str] = None
    duration_ms: Optional[int] = None
    total_codegen_calls: int = 0
    max_allowed_calls: int = 3
    is_successful: Optional[bool] = None
    total_input_tokens: int = 0
    total_output_tokens: int = 0
    avg_tokens_per_call: Optional[float] = None


@dataclass
class CallMetrics:
    """单次nl2code调用的指标数据"""
    call_id: str
    task_id: str
    call_sequence: int
    tool_name: str
    start_time: str
    end_time: Optional[str] = None
    duration_ms: Optional[int] = None
    input_tokens: int = 0
    output_tokens: int = 0
    execution_success: Optional[bool] = None
    error_message: Optional[str] = None
    code_generated: Optional[str] = None
    action_manager_enabled: bool = False
    scenario_detected: Optional[str] = None


class CodegenMetricsTracker:
    """Codegen运营指标追踪器"""
    
    def __init__(self, data_dir: str = "metrics/codegen", enabled: bool = True, es_storage: Optional[MetricsESStorage] = None):
        self.data_dir = Path(data_dir)
        self.enabled = enabled
        self.es_storage = es_storage
        self._lock = Lock()
        
        if self.enabled:
            # 确保数据目录存在
            self.data_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"CodegenMetricsTracker initialized with data_dir: {self.data_dir}, ES storage: {es_storage is not None}")
        else:
            logger.info("CodegenMetricsTracker disabled")
    
    def _get_date_str(self) -> str:
        """获取当前日期字符串"""
        return datetime.now().strftime("%Y%m%d")
    
    def _get_timestamp(self) -> str:
        """获取ISO格式时间戳"""
        return datetime.now(timezone.utc).isoformat()
    
    def _write_jsonl(self, filename: str, data: Dict[str, Any]) -> None:
        """写入JSONL文件"""
        if not self.enabled:
            return
            
        try:
            with self._lock:
                filepath = self.data_dir / filename
                with open(filepath, 'a', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False)
                    f.write('\n')
        except Exception as e:
            logger.error(f"Failed to write metrics to {filename}: {e}")
    
    def _save_to_es(self, data_type: str, data: Dict[str, Any]) -> None:
        """保存数据到ES"""
        if not self.enabled or not self.es_storage:
            return
            
        try:
            if data_type == "task":
                self.es_storage.save_task_metrics(data)
            elif data_type == "call":
                self.es_storage.save_call_metrics(data)
            else:
                logger.warning(f"Unknown data type for ES storage: {data_type}")
        except Exception as e:
            logger.error(f"Failed to save {data_type} metrics to ES: {e}")
    
    def start_task(self, session_id: str, user_id: str, task_type: str, 
                   task_instruction: str, max_allowed_calls: int = 3) -> str:
        """开始追踪一个任务"""
        task_id = str(uuid.uuid4())
        
        task_metrics = TaskMetrics(
            task_id=task_id,
            session_id=session_id,
            user_id=user_id,
            task_type=task_type,
            task_instruction=task_instruction,
            start_time=self._get_timestamp(),
            max_allowed_calls=max_allowed_calls
        )
        
        date_str = self._get_date_str()
        filename = f"task_metrics_{date_str}.jsonl"
        task_data = asdict(task_metrics)
        self._write_jsonl(filename, task_data)
        
        # 保存到ES
        self._save_to_es("task", task_data)
        
        logger.info(f"Started tracking task: {task_id}")
        return task_id
    
    def finish_task(self, task_id: str, is_successful: bool, 
                   total_codegen_calls: int,
                   total_input_tokens: int = 0, total_output_tokens: int = 0,
                   start_time_ms: Optional[float] = None) -> None:
        """完成任务追踪"""
        end_time = self._get_timestamp()
        
        # 计算任务耗时
        duration_ms = None
        if start_time_ms is not None:
            end_time_ms = time.time() * 1000
            duration_ms = int(end_time_ms - start_time_ms)
        
        # 计算平均token使用量
        avg_tokens_per_call = None
        if total_codegen_calls > 0:
            avg_tokens_per_call = (total_input_tokens + total_output_tokens) / total_codegen_calls
        
        # 读取现有记录并更新
        task_metrics = TaskMetrics(
            task_id=task_id,
            session_id="",  # 这些字段在完成时更新
            user_id="",
            task_type="",
            task_instruction="",
            start_time="",
            end_time=end_time,
            duration_ms=duration_ms,
            total_codegen_calls=total_codegen_calls,
            is_successful=is_successful,
            total_input_tokens=total_input_tokens,
            total_output_tokens=total_output_tokens,
            avg_tokens_per_call=avg_tokens_per_call
        )
        
        # 为了简化，这里直接写入完成记录
        # 在生产环境中可能需要更复杂的更新逻辑
        date_str = self._get_date_str()
        filename = f"task_completion_{date_str}.jsonl"
        completion_data = {
            "task_id": task_id,
            "end_time": end_time,
            "duration_ms": duration_ms,
            "is_successful": is_successful,
            "total_codegen_calls": total_codegen_calls,
            "total_input_tokens": total_input_tokens,
            "total_output_tokens": total_output_tokens,
            "avg_tokens_per_call": avg_tokens_per_call
        }
        self._write_jsonl(filename, completion_data)
        
        # 保存到ES
        self._save_to_es("task", completion_data)
        
        logger.info(f"Finished tracking task: {task_id}, success: {is_successful}")
    
    def track_codegen_call(self, task_id: str, call_sequence: int, tool_name: str,
                          input_tokens: int = 0, output_tokens: int = 0,
                          execution_success: Optional[bool] = None,
                          error_message: Optional[str] = None,
                          code_generated: Optional[str] = None,
                          action_manager_enabled: bool = False,
                          scenario_detected: Optional[str] = None) -> str:
        """追踪单次codegen调用"""
        call_id = str(uuid.uuid4())
        start_time = self._get_timestamp()
        
        call_metrics = CallMetrics(
            call_id=call_id,
            task_id=task_id,
            call_sequence=call_sequence,
            tool_name=tool_name,
            start_time=start_time,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            execution_success=execution_success,
            error_message=error_message,
            code_generated=code_generated,
            action_manager_enabled=action_manager_enabled,
            scenario_detected=scenario_detected
        )
        
        date_str = self._get_date_str()
        filename = f"call_metrics_{date_str}.jsonl"
        call_data = asdict(call_metrics)
        self._write_jsonl(filename, call_data)
        
        # 保存到ES
        self._save_to_es("call", call_data)
        
        logger.info(f"Tracked codegen call: {call_id} for task: {task_id}")
        return call_id
    
    def finish_codegen_call(self, call_id: str, execution_success: bool,
                           duration_ms: int, error_message: Optional[str] = None) -> None:
        """完成codegen调用追踪"""
        end_time = self._get_timestamp()
        
        completion_data = {
            "call_id": call_id,
            "end_time": end_time,
            "duration_ms": duration_ms,
            "execution_success": execution_success,
            "error_message": error_message
        }
        
        date_str = self._get_date_str()
        filename = f"call_completion_{date_str}.jsonl"
        self._write_jsonl(filename, completion_data)
        
        # 保存到ES
        self._save_to_es("call", completion_data)
        
        logger.info(f"Finished tracking call: {call_id}, success: {execution_success}")


# 全局实例
_tracker_instance: Optional[CodegenMetricsTracker] = None


def get_metrics_tracker() -> CodegenMetricsTracker:
    """获取全局metrics tracker实例"""
    global _tracker_instance
    if _tracker_instance is None:
        # 从配置中读取设置
        from common.share.config import appConfig
        
        try:
            enabled = appConfig.metrics.codegen.enabled
            data_dir = appConfig.metrics.codegen.data_dir
        except AttributeError:
            # 如果配置不存在，使用默认值
            enabled = True
            data_dir = 'metrics/codegen'
        
        # 初始化ES存储
        es_storage = None
        try:
            es_config = appConfig.automic.nl2sql.es
            app_id = appConfig.app_id or "default"
            es_storage = MetricsESStorage(es_config, app_id)
        except AttributeError:
            logger.warning("ES configuration not found, ES storage disabled for metrics")
        except Exception as e:
            logger.error(f"Failed to initialize ES storage for metrics: {e}")
        
        _tracker_instance = CodegenMetricsTracker(data_dir=data_dir, enabled=enabled, es_storage=es_storage)
    
    return _tracker_instance