import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from common.logger.logger import logger


@dataclass
class MetricsSummary:
    """指标汇总数据"""
    # 功能正确性 (在k次调用内成功完成任务的比例)
    functional_correctness: float
    
    # 执行成功率 (代码执行成功的比例)
    execution_success_rate: float
    
    # 响应时间
    avg_task_response_time_ms: float
    avg_call_response_time_ms: float
    
    # Token使用量
    avg_task_tokens: float
    avg_call_tokens: float
    
    # 统计数据
    total_tasks: int
    total_calls: int
    successful_tasks: int
    successful_calls: int


class CodegenMetricsService:
    """Codegen指标计算和查询服务"""
    
    def __init__(self, data_dir: str = "metrics/codegen"):
        self.data_dir = Path(data_dir)
        
    def _read_jsonl(self, filename: str) -> List[Dict[str, Any]]:
        """读取JSONL文件"""
        filepath = self.data_dir / filename
        if not filepath.exists():
            return []
        
        data = []
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        data.append(json.loads(line))
        except Exception as e:
            logger.error(f"Failed to read {filename}: {e}")
        
        return data
    
    def _get_date_range_files(self, start_date: str, end_date: str, file_prefix: str) -> List[str]:
        """获取日期范围内的文件列表"""
        start_dt = datetime.strptime(start_date, "%Y%m%d")
        end_dt = datetime.strptime(end_date, "%Y%m%d")
        
        files = []
        current_dt = start_dt
        while current_dt <= end_dt:
            date_str = current_dt.strftime("%Y%m%d")
            filename = f"{file_prefix}_{date_str}.jsonl"
            files.append(filename)
            current_dt += timedelta(days=1)
        
        return files
    
    def _merge_task_data(self, task_files: List[str], completion_files: List[str]) -> List[Dict[str, Any]]:
        """合并task数据和completion数据"""
        # 读取所有task数据
        all_tasks = {}
        for filename in task_files:
            tasks = self._read_jsonl(filename)
            for task in tasks:
                all_tasks[task['task_id']] = task
        
        # 读取所有completion数据并合并
        for filename in completion_files:
            completions = self._read_jsonl(filename)
            for completion in completions:
                task_id = completion['task_id']
                if task_id in all_tasks:
                    all_tasks[task_id].update(completion)
        
        return list(all_tasks.values())
    
    def get_tasks_data(self, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """获取指定日期范围的任务数据"""
        task_files = self._get_date_range_files(start_date, end_date, "task_metrics")
        completion_files = self._get_date_range_files(start_date, end_date, "task_completion")
        
        return self._merge_task_data(task_files, completion_files)
    
    def get_calls_data(self, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """获取指定日期范围的调用数据"""
        call_files = self._get_date_range_files(start_date, end_date, "call_metrics")
        completion_files = self._get_date_range_files(start_date, end_date, "call_completion")
        
        # 读取所有call数据
        all_calls = {}
        for filename in call_files:
            calls = self._read_jsonl(filename)
            for call in calls:
                all_calls[call['call_id']] = call
        
        # 读取所有completion数据并合并
        for filename in completion_files:
            completions = self._read_jsonl(filename)
            for completion in completions:
                call_id = completion['call_id']
                if call_id in all_calls:
                    all_calls[call_id].update(completion)
        
        return list(all_calls.values())
    
    def calculate_functional_correctness(self, tasks: List[Dict[str, Any]], k: int = 3) -> float:
        """计算功能正确性：在k次调用内成功完成任务的比例"""
        if not tasks:
            return 0.0
        
        completed_tasks = [t for t in tasks if t.get('end_time') is not None]
        if not completed_tasks:
            return 0.0
        
        successful_within_k = sum(1 for task in completed_tasks 
                                 if task.get('is_successful', False) and 
                                    task.get('total_codegen_calls', float('inf')) <= k)
        
        return successful_within_k / len(completed_tasks)
    
    def calculate_functional_correctness_by_k(self, tasks: List[Dict[str, Any]], k_values: List[int]) -> Dict[int, float]:
        """计算多个k值下的功能正确性"""
        return {k: self.calculate_functional_correctness(tasks, k) for k in k_values}
    
    def calculate_execution_success_rate(self, calls: List[Dict[str, Any]]) -> float:
        """计算执行成功率：代码执行成功的比例"""
        if not calls:
            return 0.0
        
        completed_calls = [c for c in calls if c.get('end_time') is not None]
        if not completed_calls:
            return 0.0
        
        successful_calls = sum(1 for call in completed_calls 
                              if call.get('execution_success', False))
        
        return successful_calls / len(completed_calls)
    
    def calculate_execution_success_rate_by_k(self, tasks: List[Dict[str, Any]], calls: List[Dict[str, Any]], k_values: List[int]) -> Dict[int, float]:
        """计算多个k值下的执行成功率：在k次调用内至少有一次成功执行的任务比例"""
        if not tasks:
            return {k: 0.0 for k in k_values}
        
        # 构建任务ID到调用的映射
        task_to_calls = {}
        for call in calls:
            task_id = call.get('task_id')
            if task_id:
                if task_id not in task_to_calls:
                    task_to_calls[task_id] = []
                task_to_calls[task_id].append(call)
        
        completed_tasks = [t for t in tasks if t.get('end_time') is not None]
        if not completed_tasks:
            return {k: 0.0 for k in k_values}
        
        result = {}
        for k in k_values:
            successful_tasks = 0
            for task in completed_tasks:
                task_id = task.get('task_id')
                task_calls = task_to_calls.get(task_id, [])
                
                # 检查前k次调用中是否有成功的
                completed_calls = [c for c in task_calls if c.get('end_time') is not None]
                completed_calls.sort(key=lambda x: x.get('call_sequence', 0))
                
                first_k_calls = completed_calls[:k]
                has_success = any(call.get('execution_success', False) for call in first_k_calls)
                
                if has_success:
                    successful_tasks += 1
            
            result[k] = successful_tasks / len(completed_tasks)
        
        return result
    
    def calculate_avg_response_time(self, data: List[Dict[str, Any]]) -> float:
        """计算平均响应时间"""
        completed_items = [item for item in data 
                          if item.get('duration_ms') is not None]
        
        if not completed_items:
            return 0.0
        
        total_duration = sum(item['duration_ms'] for item in completed_items)
        return total_duration / len(completed_items)
    
    def calculate_avg_tokens(self, data: List[Dict[str, Any]]) -> float:
        """计算平均token使用量"""
        items_with_tokens = []
        
        for item in data:
            total_tokens = 0
            if 'total_input_tokens' in item and 'total_output_tokens' in item:
                total_tokens = item['total_input_tokens'] + item['total_output_tokens']
            elif 'input_tokens' in item and 'output_tokens' in item:
                total_tokens = item['input_tokens'] + item['output_tokens']
            
            if total_tokens > 0:
                items_with_tokens.append(total_tokens)
        
        if not items_with_tokens:
            return 0.0
        
        return sum(items_with_tokens) / len(items_with_tokens)
    
    def get_metrics_summary(self, start_date: str, end_date: str, k: int = 3) -> MetricsSummary:
        """获取指标汇总"""
        tasks = self.get_tasks_data(start_date, end_date)
        calls = self.get_calls_data(start_date, end_date)
        
        # 计算各项指标
        functional_correctness = self.calculate_functional_correctness(tasks, k)
        execution_success_rate = self.calculate_execution_success_rate(calls)
        
        avg_task_response_time = self.calculate_avg_response_time(tasks)
        avg_call_response_time = self.calculate_avg_response_time(calls)
        
        avg_task_tokens = self.calculate_avg_tokens(tasks)
        avg_call_tokens = self.calculate_avg_tokens(calls)
        
        # 统计数据
        completed_tasks = [t for t in tasks if t.get('end_time') is not None]
        completed_calls = [c for c in calls if c.get('end_time') is not None]
        
        successful_tasks = sum(1 for task in completed_tasks 
                              if task.get('is_successful', False))
        successful_calls = sum(1 for call in completed_calls 
                              if call.get('execution_success', False))
        
        return MetricsSummary(
            functional_correctness=functional_correctness,
            execution_success_rate=execution_success_rate,
            avg_task_response_time_ms=avg_task_response_time,
            avg_call_response_time_ms=avg_call_response_time,
            avg_task_tokens=avg_task_tokens,
            avg_call_tokens=avg_call_tokens,
            total_tasks=len(completed_tasks),
            total_calls=len(completed_calls),
            successful_tasks=successful_tasks,
            successful_calls=successful_calls
        )
    
    def get_daily_metrics(self, date: str, k: int = 3) -> MetricsSummary:
        """获取单日指标"""
        return self.get_metrics_summary(date, date, k)
    
    def get_weekly_metrics(self, end_date: str, k: int = 3) -> MetricsSummary:
        """获取周指标（过去7天）"""
        end_dt = datetime.strptime(end_date, "%Y%m%d")
        start_dt = end_dt - timedelta(days=6)
        start_date = start_dt.strftime("%Y%m%d")
        
        return self.get_metrics_summary(start_date, end_date, k)
    
    def get_monthly_metrics(self, year: int, month: int, k: int = 3) -> MetricsSummary:
        """获取月指标"""
        start_date = f"{year:04d}{month:02d}01"
        
        # 计算月末日期
        if month == 12:
            next_month_start = datetime(year + 1, 1, 1)
        else:
            next_month_start = datetime(year, month + 1, 1)
        
        month_end = next_month_start - timedelta(days=1)
        end_date = month_end.strftime("%Y%m%d")
        
        return self.get_metrics_summary(start_date, end_date, k)
    
    def get_user_metrics(self, user_id: str, start_date: str, end_date: str, k: int = 3) -> MetricsSummary:
        """获取特定用户的指标"""
        all_tasks = self.get_tasks_data(start_date, end_date)
        all_calls = self.get_calls_data(start_date, end_date)
        
        # 过滤特定用户的数据
        user_tasks = [t for t in all_tasks if t.get('user_id') == user_id]
        user_task_ids = {t['task_id'] for t in user_tasks}
        user_calls = [c for c in all_calls if c.get('task_id') in user_task_ids]
        
        # 计算用户指标（复用现有方法）
        functional_correctness = self.calculate_functional_correctness(user_tasks, k)
        execution_success_rate = self.calculate_execution_success_rate(user_calls)
        
        avg_task_response_time = self.calculate_avg_response_time(user_tasks)
        avg_call_response_time = self.calculate_avg_response_time(user_calls)
        
        avg_task_tokens = self.calculate_avg_tokens(user_tasks)
        avg_call_tokens = self.calculate_avg_tokens(user_calls)
        
        # 统计数据
        completed_tasks = [t for t in user_tasks if t.get('end_time') is not None]
        completed_calls = [c for c in user_calls if c.get('end_time') is not None]
        
        successful_tasks = sum(1 for task in completed_tasks 
                              if task.get('is_successful', False))
        successful_calls = sum(1 for call in completed_calls 
                              if call.get('execution_success', False))
        
        return MetricsSummary(
            functional_correctness=functional_correctness,
            execution_success_rate=execution_success_rate,
            avg_task_response_time_ms=avg_task_response_time,
            avg_call_response_time_ms=avg_call_response_time,
            avg_task_tokens=avg_task_tokens,
            avg_call_tokens=avg_call_tokens,
            total_tasks=len(completed_tasks),
            total_calls=len(completed_calls),
            successful_tasks=successful_tasks,
            successful_calls=successful_calls
        )
    
    def export_daily_summary(self, date: str) -> None:
        """导出日常汇总到JSON文件"""
        summary = self.get_daily_metrics(date)
        
        output_file = self.data_dir / f"daily_summary_{date}.json"
        summary_data = {
            "date": date,
            "metrics": {
                "functional_correctness": summary.functional_correctness,
                "execution_success_rate": summary.execution_success_rate,
                "avg_task_response_time_ms": summary.avg_task_response_time_ms,
                "avg_call_response_time_ms": summary.avg_call_response_time_ms,
                "avg_task_tokens": summary.avg_task_tokens,
                "avg_call_tokens": summary.avg_call_tokens
            },
            "statistics": {
                "total_tasks": summary.total_tasks,
                "total_calls": summary.total_calls,
                "successful_tasks": summary.successful_tasks,
                "successful_calls": summary.successful_calls
            },
            "generated_at": datetime.now().isoformat()
        }
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, ensure_ascii=False, indent=2)
            logger.info(f"Daily summary exported to {output_file}")
        except Exception as e:
            logger.error(f"Failed to export daily summary: {e}")


# 全局实例
_service_instance: Optional[CodegenMetricsService] = None


def get_metrics_service() -> CodegenMetricsService:
    """获取全局metrics service实例"""
    global _service_instance
    if _service_instance is None:
        from common.share.config import appConfig
        try:
            data_dir = appConfig.metrics.codegen.data_dir
        except AttributeError:
            data_dir = 'metrics/codegen'
        _service_instance = CodegenMetricsService(data_dir=data_dir)
    
    return _service_instance