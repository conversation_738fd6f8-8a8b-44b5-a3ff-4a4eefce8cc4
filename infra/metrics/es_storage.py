"""
Elasticsearch storage interface for metrics and replay data
Based on the nl2sql ES vector store implementation
"""
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from dataclasses import asdict
from pydantic import BaseModel

from common.database.es_operator import ESOperator
from common.logger import logger
from common.share.config import ESConfig
from infra.metrics.replay_data_models import (
    UserSessionReplay, AgentReasoningChain, CodegenContextReplay,
    CodeExecutionReplay, MCPCallChain, DataAccessTrace,
    EnvironmentSnapshot, TimeSeriesEvent
)

logger = logger.get_logger(__name__)


class MetricsESQueryParams(BaseModel):
    """Metrics ES query parameters"""
    index_name: str
    query_body: Dict[str, Any]
    size: int = 1000
    from_: int = 0
    sort: Optional[List[Dict[str, Any]]] = None


class MetricsESStorage(ESOperator):
    """Elasticsearch storage for metrics data"""
    
    def __init__(self, conf: ESConfig, app_id: str):
        super().__init__(conf)
        self.app_id = app_id
        self.metrics_index = f"{self.config.index_name}_metrics_{self.app_id}".lower()
        self.task_metrics_index = f"{self.config.index_name}_task_metrics_{self.app_id}".lower()
        self.call_metrics_index = f"{self.config.index_name}_call_metrics_{self.app_id}".lower()
        self._ensure_metrics_indices_exist()
    
    def _ensure_metrics_indices_exist(self):
        """Ensure metrics indices exist"""
        # Task metrics index
        task_metrics_mapping = {
            "settings": {
                "number_of_shards": 3,
                "refresh_interval": "1s"
            },
            "mappings": {
                "dynamic": "true",
                "properties": {
                    "app_id": {"type": "keyword"},
                    "task_id": {"type": "keyword"},
                    "user_id": {"type": "keyword"},
                    "session_id": {"type": "keyword"},
                    "start_time": {"type": "date"},
                    "end_time": {"type": "date"},
                    "duration_ms": {"type": "integer"},
                    "is_successful": {"type": "boolean"},
                    "total_codegen_calls": {"type": "integer"},
                    "total_input_tokens": {"type": "long"},
                    "total_output_tokens": {"type": "long"},
                    "task_type": {"type": "keyword"},
                    "task_complexity": {"type": "keyword"},
                    "error_type": {"type": "keyword"},
                    "error_message": {"type": "text"},
                    "created_at": {"type": "date"}
                }
            }
        }
        
        # Call metrics index
        call_metrics_mapping = {
            "settings": {
                "number_of_shards": 3,
                "refresh_interval": "1s"
            },
            "mappings": {
                "dynamic": "true",
                "properties": {
                    "app_id": {"type": "keyword"},
                    "call_id": {"type": "keyword"},
                    "task_id": {"type": "keyword"},
                    "call_sequence": {"type": "integer"},
                    "start_time": {"type": "date"},
                    "end_time": {"type": "date"},
                    "duration_ms": {"type": "integer"},
                    "execution_success": {"type": "boolean"},
                    "model_name": {"type": "keyword"},
                    "input_tokens": {"type": "long"},
                    "output_tokens": {"type": "long"},
                    "generated_code": {"type": "text"},
                    "execution_output": {"type": "text"},
                    "execution_error": {"type": "text"},
                    "created_at": {"type": "date"}
                }
            }
        }
        
        indices_mappings = [
            (self.task_metrics_index, task_metrics_mapping),
            (self.call_metrics_index, call_metrics_mapping)
        ]
        
        for index_name, mapping in indices_mappings:
            try:
                if not self.sync_client.indices.exists(index=index_name):
                    self.sync_client.indices.create(index=index_name, body=mapping)
                    logger.info(f"Created metrics index {index_name} successfully")
            except Exception as e:
                logger.error(f"Failed to create metrics index {index_name}: {e}")
                raise
    
    @ESOperator._retry_decorator
    def save_task_metrics(self, task_data: Dict[str, Any]) -> str:
        """Save task metrics data"""
        task_data['app_id'] = self.app_id
        task_data['created_at'] = datetime.now(timezone.utc).isoformat()
        
        response = self.sync_client.index(
            index=self.task_metrics_index,
            document=task_data
        )
        return response["_id"]
    
    @ESOperator._retry_decorator
    def save_call_metrics(self, call_data: Dict[str, Any]) -> str:
        """Save call metrics data"""
        call_data['app_id'] = self.app_id
        call_data['created_at'] = datetime.now(timezone.utc).isoformat()
        
        response = self.sync_client.index(
            index=self.call_metrics_index,
            document=call_data
        )
        return response["_id"]
    
    @ESOperator._retry_decorator
    def query_task_metrics(self, query_params: MetricsESQueryParams) -> List[Dict[str, Any]]:
        """Query task metrics"""
        query_body = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"app_id": self.app_id}},
                        *query_params.query_body.get("must", [])
                    ],
                    "filter": query_params.query_body.get("filter", []),
                    "should": query_params.query_body.get("should", [])
                }
            },
            "size": query_params.size,
            "from": query_params.from_,
            "_source": True
        }
        
        if query_params.sort:
            query_body["sort"] = query_params.sort
        
        response = self.sync_client.search(
            index=self.task_metrics_index,
            body=query_body
        )
        
        return [hit["_source"] for hit in response["hits"]["hits"]]
    
    @ESOperator._retry_decorator
    def query_call_metrics(self, query_params: MetricsESQueryParams) -> List[Dict[str, Any]]:
        """Query call metrics"""
        query_body = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"app_id": self.app_id}},
                        *query_params.query_body.get("must", [])
                    ],
                    "filter": query_params.query_body.get("filter", []),
                    "should": query_params.query_body.get("should", [])
                }
            },
            "size": query_params.size,
            "from": query_params.from_,
            "_source": True
        }
        
        if query_params.sort:
            query_body["sort"] = query_params.sort
        
        response = self.sync_client.search(
            index=self.call_metrics_index,
            body=query_body
        )
        
        return [hit["_source"] for hit in response["hits"]["hits"]]
    
    @ESOperator._retry_decorator
    def get_daily_metrics_summary(self, date: str) -> Dict[str, Any]:
        """Get daily metrics summary"""
        start_time = f"{date}T00:00:00Z"
        end_time = f"{date}T23:59:59Z"
        
        query_body = {
            "must": [],
            "filter": [
                {
                    "range": {
                        "start_time": {
                            "gte": start_time,
                            "lte": end_time
                        }
                    }
                }
            ]
        }
        
        # Get task metrics
        task_query = MetricsESQueryParams(
            index_name=self.task_metrics_index,
            query_body=query_body,
            size=10000
        )
        task_metrics = self.query_task_metrics(task_query)
        
        # Get call metrics
        call_query = MetricsESQueryParams(
            index_name=self.call_metrics_index,
            query_body=query_body,
            size=10000
        )
        call_metrics = self.query_call_metrics(call_query)
        
        # Calculate summary
        total_tasks = len(task_metrics)
        successful_tasks = sum(1 for task in task_metrics if task.get('is_successful', False))
        total_calls = len(call_metrics)
        successful_calls = sum(1 for call in call_metrics if call.get('execution_success', False))
        
        return {
            "date": date,
            "total_tasks": total_tasks,
            "successful_tasks": successful_tasks,
            "total_calls": total_calls,
            "successful_calls": successful_calls,
            "functional_correctness": successful_tasks / total_tasks if total_tasks > 0 else 0,
            "execution_success_rate": successful_calls / total_calls if total_calls > 0 else 0,
            "task_metrics": task_metrics,
            "call_metrics": call_metrics
        }


class ReplayDataESStorage(ESOperator):
    """Elasticsearch storage for replay data"""
    
    def __init__(self, conf: ESConfig, app_id: str):
        super().__init__(conf)
        self.app_id = app_id
        self.session_index = f"{self.config.index_name}_session_{self.app_id}".lower()
        self.reasoning_index = f"{self.config.index_name}_reasoning_{self.app_id}".lower()
        self.codegen_index = f"{self.config.index_name}_codegen_{self.app_id}".lower()
        self.execution_index = f"{self.config.index_name}_execution_{self.app_id}".lower()
        self.mcp_index = f"{self.config.index_name}_mcp_{self.app_id}".lower()
        self.data_access_index = f"{self.config.index_name}_data_access_{self.app_id}".lower()
        self.environment_index = f"{self.config.index_name}_environment_{self.app_id}".lower()
        self.events_index = f"{self.config.index_name}_events_{self.app_id}".lower()
        self._ensure_replay_indices_exist()
    
    def _ensure_replay_indices_exist(self):
        """Ensure replay data indices exist"""
        # Session replay index
        session_mapping = {
            "settings": {
                "number_of_shards": 3,
                "refresh_interval": "1s"
            },
            "mappings": {
                "dynamic": "true",
                "properties": {
                    "app_id": {"type": "keyword"},
                    "session_id": {"type": "keyword"},
                    "user_id": {"type": "keyword"},
                    "start_time": {"type": "date"},
                    "end_time": {"type": "date"},
                    "initial_query": {"type": "text"},
                    "conversation_turns": {"type": "nested"},
                    "user_language": {"type": "keyword"},
                    "final_satisfaction": {"type": "integer"},
                    "created_at": {"type": "date"}
                }
            }
        }
        
        # Reasoning chain index
        reasoning_mapping = {
            "settings": {
                "number_of_shards": 3,
                "refresh_interval": "1s"
            },
            "mappings": {
                "dynamic": "true",
                "properties": {
                    "app_id": {"type": "keyword"},
                    "reasoning_id": {"type": "keyword"},
                    "session_id": {"type": "keyword"},
                    "task_id": {"type": "keyword"},
                    "subtask_sequence": {"type": "integer"},
                    "timestamp": {"type": "date"},
                    "agent_state_before": {"type": "object"},
                    "agent_state_after": {"type": "object"},
                    "tool_selection_reasoning": {"type": "text"},
                    "graph_node": {"type": "keyword"},
                    "created_at": {"type": "date"}
                }
            }
        }
        
        # Codegen context index
        codegen_mapping = {
            "settings": {
                "number_of_shards": 3,
                "refresh_interval": "1s"
            },
            "mappings": {
                "dynamic": "true",
                "properties": {
                    "app_id": {"type": "keyword"},
                    "context_id": {"type": "keyword"},
                    "task_id": {"type": "keyword"},
                    "call_id": {"type": "keyword"},
                    "session_id": {"type": "keyword"},
                    "call_sequence": {"type": "integer"},
                    "timestamp": {"type": "date"},
                    "full_prompt": {"type": "text"},
                    "model_name": {"type": "keyword"},
                    "generated_code": {"type": "text"},
                    "confidence_score": {"type": "float"},
                    "created_at": {"type": "date"}
                }
            }
        }
        
        # Execution replay index
        execution_mapping = {
            "settings": {
                "number_of_shards": 3,
                "refresh_interval": "1s"
            },
            "mappings": {
                "dynamic": "true",
                "properties": {
                    "app_id": {"type": "keyword"},
                    "execution_id": {"type": "keyword"},
                    "task_id": {"type": "keyword"},
                    "call_id": {"type": "keyword"},
                    "timestamp": {"type": "date"},
                    "code_before_execution": {"type": "text"},
                    "execution_success": {"type": "boolean"},
                    "execution_output": {"type": "text"},
                    "execution_errors": {"type": "text"},
                    "execution_time_ms": {"type": "integer"},
                    "memory_usage_mb": {"type": "float"},
                    "created_at": {"type": "date"}
                }
            }
        }
        
        # Time series events index
        events_mapping = {
            "settings": {
                "number_of_shards": 5,
                "refresh_interval": "1s"
            },
            "mappings": {
                "dynamic": "true",
                "properties": {
                    "app_id": {"type": "keyword"},
                    "event_id": {"type": "keyword"},
                    "session_id": {"type": "keyword"},
                    "timestamp_ms": {"type": "long"},
                    "event_type": {"type": "keyword"},
                    "sequence_number": {"type": "integer"},
                    "parent_event_id": {"type": "keyword"},
                    "event_data": {"type": "object"},
                    "created_at": {"type": "date"}
                }
            }
        }
        
        indices_mappings = [
            (self.session_index, session_mapping),
            (self.reasoning_index, reasoning_mapping),
            (self.codegen_index, codegen_mapping),
            (self.execution_index, execution_mapping),
            (self.events_index, events_mapping)
        ]
        
        for index_name, mapping in indices_mappings:
            try:
                if not self.sync_client.indices.exists(index=index_name):
                    self.sync_client.indices.create(index=index_name, body=mapping)
                    logger.info(f"Created replay index {index_name} successfully")
            except Exception as e:
                logger.error(f"Failed to create replay index {index_name}: {e}")
                raise
    
    @ESOperator._retry_decorator
    def save_session_replay(self, session_data: UserSessionReplay) -> str:
        """Save session replay data"""
        data = asdict(session_data)
        data['app_id'] = self.app_id
        data['created_at'] = datetime.now(timezone.utc).isoformat()
        
        response = self.sync_client.index(
            index=self.session_index,
            document=data
        )
        return response["_id"]
    
    @ESOperator._retry_decorator
    def save_reasoning_chain(self, reasoning_data: AgentReasoningChain) -> str:
        """Save reasoning chain data"""
        data = asdict(reasoning_data)
        data['app_id'] = self.app_id
        data['created_at'] = datetime.now(timezone.utc).isoformat()
        
        response = self.sync_client.index(
            index=self.reasoning_index,
            document=data
        )
        return response["_id"]
    
    @ESOperator._retry_decorator
    def save_codegen_context(self, codegen_data: CodegenContextReplay) -> str:
        """Save codegen context data"""
        data = asdict(codegen_data)
        data['app_id'] = self.app_id
        data['created_at'] = datetime.now(timezone.utc).isoformat()
        
        response = self.sync_client.index(
            index=self.codegen_index,
            document=data
        )
        return response["_id"]
    
    @ESOperator._retry_decorator
    def save_execution_replay(self, execution_data: CodeExecutionReplay) -> str:
        """Save execution replay data"""
        data = asdict(execution_data)
        data['app_id'] = self.app_id
        data['created_at'] = datetime.now(timezone.utc).isoformat()
        
        response = self.sync_client.index(
            index=self.execution_index,
            document=data
        )
        return response["_id"]
    
    @ESOperator._retry_decorator
    def save_time_series_event(self, event_data: TimeSeriesEvent) -> str:
        """Save time series event data"""
        data = asdict(event_data)
        data['app_id'] = self.app_id
        data['created_at'] = datetime.now(timezone.utc).isoformat()
        
        response = self.sync_client.index(
            index=self.events_index,
            document=data
        )
        return response["_id"]
    
    @ESOperator._retry_decorator
    def query_session_replay(self, query_params: MetricsESQueryParams) -> List[Dict[str, Any]]:
        """Query session replay data"""
        query_body = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"app_id": self.app_id}},
                        *query_params.query_body.get("must", [])
                    ],
                    "filter": query_params.query_body.get("filter", [])
                }
            },
            "size": query_params.size,
            "from": query_params.from_,
            "_source": True
        }
        
        if query_params.sort:
            query_body["sort"] = query_params.sort
        
        response = self.sync_client.search(
            index=self.session_index,
            body=query_body
        )
        
        return [hit["_source"] for hit in response["hits"]["hits"]]
    
    @ESOperator._retry_decorator
    def query_replay_data_by_session(self, session_id: str) -> Dict[str, Any]:
        """Query all replay data for a specific session"""
        query_body = {
            "must": [{"term": {"session_id": session_id}}],
            "filter": []
        }
        
        query_params = MetricsESQueryParams(
            index_name="",
            query_body=query_body,
            size=10000
        )
        
        # Get all related data
        session_data = self.query_session_replay(query_params)
        
        # Query reasoning chains
        reasoning_response = self.sync_client.search(
            index=self.reasoning_index,
            body={
                "query": {
                    "bool": {
                        "must": [
                            {"term": {"app_id": self.app_id}},
                            {"term": {"session_id": session_id}}
                        ]
                    }
                },
                "size": 10000
            }
        )
        reasoning_data = [hit["_source"] for hit in reasoning_response["hits"]["hits"]]
        
        # Query codegen contexts
        codegen_response = self.sync_client.search(
            index=self.codegen_index,
            body={
                "query": {
                    "bool": {
                        "must": [
                            {"term": {"app_id": self.app_id}},
                            {"term": {"session_id": session_id}}
                        ]
                    }
                },
                "size": 10000
            }
        )
        codegen_data = [hit["_source"] for hit in codegen_response["hits"]["hits"]]
        
        # Query execution replays
        execution_response = self.sync_client.search(
            index=self.execution_index,
            body={
                "query": {
                    "bool": {
                        "must": [
                            {"term": {"app_id": self.app_id}},
                            {"term": {"session_id": session_id}}
                        ]
                    }
                },
                "size": 10000
            }
        )
        execution_data = [hit["_source"] for hit in execution_response["hits"]["hits"]]
        
        # Query time series events
        events_response = self.sync_client.search(
            index=self.events_index,
            body={
                "query": {
                    "bool": {
                        "must": [
                            {"term": {"app_id": self.app_id}},
                            {"term": {"session_id": session_id}}
                        ]
                    }
                },
                "sort": [{"timestamp_ms": {"order": "asc"}}],
                "size": 10000
            }
        )
        events_data = [hit["_source"] for hit in events_response["hits"]["hits"]]
        
        return {
            "session_id": session_id,
            "session_data": session_data,
            "reasoning_chains": reasoning_data,
            "codegen_contexts": codegen_data,
            "execution_replays": execution_data,
            "time_series_events": events_data
        }