"""
离线复现数据模型定义
用于收集完整的agent执行轨迹，支持离线环境完全复现用户交互场景
"""
import json
import uuid
import time
import sys
import os
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict, field
from pathlib import Path


@dataclass
class UserSessionReplay:
    """用户会话完整记录 - 支持完整交互链路复现"""
    session_id: str
    user_id: str
    start_time: str
    end_time: Optional[str] = None
    
    # 用户交互原始数据
    initial_query: str = ""
    conversation_turns: List[Dict[str, Any]] = field(default_factory=list)
    user_inputs: List[str] = field(default_factory=list)
    agent_responses: List[str] = field(default_factory=list)
    user_feedback: List[str] = field(default_factory=list)
    
    # 会话元数据
    user_language: Optional[str] = None
    session_context: Dict[str, Any] = field(default_factory=dict)
    final_satisfaction: Optional[int] = None  # 1-5评分


@dataclass
class AgentReasoningChain:
    """Agent推理链数据 - 记录完整决策过程"""
    reasoning_id: str
    session_id: str
    task_id: str
    subtask_sequence: int
    timestamp: str
    
    # Agent状态和决策
    agent_state_before: Dict[str, Any] = field(default_factory=dict)
    agent_state_after: Dict[str, Any] = field(default_factory=dict)
    
    # 推理过程
    tool_selection_reasoning: str = ""
    plan_generation_process: Dict[str, Any] = field(default_factory=dict)
    execution_strategy: str = ""
    
    # LangGraph相关
    graph_node: str = ""
    graph_edge_condition: Dict[str, Any] = field(default_factory=dict)
    graph_state_transition: Dict[str, Any] = field(default_factory=dict)


@dataclass
class CodegenContextReplay:
    """代码生成完整上下文 - 支持代码生成复现"""
    context_id: str
    task_id: str
    call_id: str
    call_sequence: int
    timestamp: str
    session_id: str = ""  # 添加session_id字段
    
    # LLM调用完整上下文
    full_prompt: str = ""
    model_name: str = ""
    model_parameters: Dict[str, Any] = field(default_factory=dict)
    
    # 代码生成环境
    previous_code_history: List[str] = field(default_factory=list)
    execution_environment_vars: Dict[str, Any] = field(default_factory=dict)
    available_tools: List[Dict[str, Any]] = field(default_factory=list)
    data_schema_info: Dict[str, Any] = field(default_factory=dict)
    
    # 生成结果
    generated_code: str = ""
    generation_reasoning: str = ""
    confidence_score: Optional[float] = None
    
    # 增强功能状态
    action_manager_state: Dict[str, Any] = field(default_factory=dict)
    scenario_detection_result: Dict[str, Any] = field(default_factory=dict)
    library_config_applied: List[str] = field(default_factory=list)


@dataclass
class CodeExecutionReplay:
    """代码执行完整记录 - 支持执行过程复现"""
    execution_id: str
    task_id: str
    call_id: str
    timestamp: str
    
    # 执行前状态
    code_before_execution: str = ""
    environment_snapshot_before: Dict[str, Any] = field(default_factory=dict)
    available_variables: Dict[str, str] = field(default_factory=dict)  # 变量名和类型
    imported_modules: List[str] = field(default_factory=list)
    
    # 执行过程
    execution_method: str = ""  # jupyter, direct, etc.
    execution_timeout: Optional[int] = None
    
    # 执行结果
    execution_success: bool = False
    execution_output: str = ""
    execution_errors: str = ""
    execution_time_ms: Optional[int] = None
    memory_usage_mb: Optional[float] = None
    
    # 执行后状态变化
    environment_changes: Dict[str, Any] = field(default_factory=dict)
    new_variables_created: Dict[str, str] = field(default_factory=dict)
    files_created: List[str] = field(default_factory=list)
    visualizations_generated: List[str] = field(default_factory=list)


@dataclass
class MCPCallChain:
    """MCP工具调用链 - 支持工具交互复现"""
    mcp_session_id: str
    task_id: str
    call_chain_id: str
    timestamp: str
    
    # MCP会话信息
    server_info: Dict[str, Any] = field(default_factory=dict)
    connection_config: Dict[str, Any] = field(default_factory=dict)
    
    # 工具调用序列
    tool_calls_sequence: List[Dict[str, Any]] = field(default_factory=list)
    
    # 每次调用详情
    call_details: List[Dict[str, Any]] = field(default_factory=list)  # [{call_id, tool_name, input, output, duration}]
    
    # 工具状态变化
    tool_state_changes: Dict[str, Any] = field(default_factory=dict)
    

@dataclass
class DataAccessTrace:
    """数据访问轨迹 - 支持数据操作复现"""
    trace_id: str
    task_id: str
    timestamp: str
    
    # 数据源配置
    data_source_type: str = ""  # dlc, es, mysql, etc.
    data_source_config: Dict[str, Any] = field(default_factory=dict)
    connection_info: Dict[str, Any] = field(default_factory=dict)
    
    # 查询操作
    sql_queries: List[str] = field(default_factory=list)
    query_parameters: List[Dict[str, Any]] = field(default_factory=list)
    
    # 查询结果 (可配置是否保存实际数据)
    query_results_schema: List[Dict[str, Any]] = field(default_factory=list)
    query_results_sample: List[Dict[str, Any]] = field(default_factory=list)  # 采样数据
    result_row_counts: List[int] = field(default_factory=list)
    
    # 数据发现过程
    schema_discovery_process: Dict[str, Any] = field(default_factory=dict)
    table_analysis_results: Dict[str, Any] = field(default_factory=dict)


@dataclass
class EnvironmentSnapshot:
    """环境快照 - 支持环境重建"""
    snapshot_id: str
    session_id: str
    timestamp: str
    
    # 系统环境
    python_version: str = ""
    system_platform: str = ""
    
    # 包依赖
    installed_packages: Dict[str, str] = field(default_factory=dict)  # {package: version}
    requirements_txt: str = ""
    
    # 应用配置
    app_config_snapshot: Dict[str, Any] = field(default_factory=dict)
    feature_flags: Dict[str, bool] = field(default_factory=dict)
    model_configs: Dict[str, Any] = field(default_factory=dict)
    
    # 运行时环境
    environment_variables: Dict[str, str] = field(default_factory=dict)
    working_directory: str = ""
    available_resources: Dict[str, Any] = field(default_factory=dict)  # CPU, Memory, etc.


@dataclass  
class TimeSeriesEvent:
    """时间序列事件 - 支持精确时序重放"""
    event_id: str
    session_id: str
    timestamp_ms: int  # 毫秒级时间戳
    event_type: str
    
    # 事件数据
    event_data: Dict[str, Any] = field(default_factory=dict)
    related_ids: Dict[str, str] = field(default_factory=dict)  # {task_id, call_id, etc.}
    
    # 序列信息
    sequence_number: int = 0
    parent_event_id: Optional[str] = None
    

def create_replay_data_id() -> str:
    """生成复现数据专用ID"""
    return f"replay_{uuid.uuid4().hex[:12]}"


def get_current_timestamp() -> str:
    """获取当前时间戳"""
    return datetime.now(timezone.utc).isoformat()


def get_current_timestamp_ms() -> int:
    """获取当前毫秒时间戳"""
    return int(time.time() * 1000)


def serialize_replay_data(data_obj) -> Dict[str, Any]:
    """序列化复现数据对象"""
    if hasattr(data_obj, '__dataclass_fields__'):
        return asdict(data_obj)
    return data_obj


def get_environment_info() -> Dict[str, Any]:
    """获取当前环境信息"""
    import platform
    import pkg_resources
    
    # 获取已安装包信息
    installed_packages = {}
    try:
        for package in pkg_resources.working_set:
            installed_packages[package.project_name] = package.version
    except Exception:
        pass
    
    return {
        "python_version": platform.python_version(),
        "system_platform": platform.platform(),
        "installed_packages": installed_packages,
        "working_directory": os.getcwd(),
        "python_executable": sys.executable
    }