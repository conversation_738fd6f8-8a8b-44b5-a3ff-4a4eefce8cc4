from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from common.logger.logger import logger
from .codegen_metrics_service import get_metrics_service, MetricsSummary


class MetricsResponse(BaseModel):
    """指标响应模型"""
    functional_correctness: float
    execution_success_rate: float
    avg_task_response_time_ms: float
    avg_call_response_time_ms: float
    avg_task_tokens: float
    avg_call_tokens: float
    total_tasks: int
    total_calls: int
    successful_tasks: int
    successful_calls: int


class DailyMetricsResponse(BaseModel):
    """日指标响应模型"""
    date: str
    metrics: MetricsResponse


class PeriodMetricsResponse(BaseModel):
    """时期指标响应模型"""
    start_date: str
    end_date: str
    metrics: MetricsResponse


def create_metrics_router() -> APIRouter:
    """创建metrics API router"""
    router = APIRouter(prefix="/metrics/codegen", tags=["codegen-metrics"])
    service = get_metrics_service()
    
    def _summary_to_response(summary: MetricsSummary) -> MetricsResponse:
        """将MetricsSummary转换为响应模型"""
        return MetricsResponse(
            functional_correctness=summary.functional_correctness,
            execution_success_rate=summary.execution_success_rate,
            avg_task_response_time_ms=summary.avg_task_response_time_ms,
            avg_call_response_time_ms=summary.avg_call_response_time_ms,
            avg_task_tokens=summary.avg_task_tokens,
            avg_call_tokens=summary.avg_call_tokens,
            total_tasks=summary.total_tasks,
            total_calls=summary.total_calls,
            successful_tasks=summary.successful_tasks,
            successful_calls=summary.successful_calls
        )
    
    @router.get("/summary", response_model=MetricsResponse)
    async def get_overall_summary(
        start_date: str = Query(..., description="开始日期 (YYYYMMDD)", regex=r'^\d{8}$'),
        end_date: str = Query(..., description="结束日期 (YYYYMMDD)", regex=r'^\d{8}$'),
        k: int = Query(3, description="最大允许的codegen调用次数", ge=1, le=10)
    ):
        """获取整体指标汇总"""
        try:
            # 验证日期范围
            start_dt = datetime.strptime(start_date, "%Y%m%d")
            end_dt = datetime.strptime(end_date, "%Y%m%d")
            
            if start_dt > end_dt:
                raise HTTPException(status_code=400, detail="开始日期不能晚于结束日期")
            
            if (end_dt - start_dt).days > 365:
                raise HTTPException(status_code=400, detail="日期范围不能超过365天")
            
            summary = service.get_metrics_summary(start_date, end_date, k)
            return _summary_to_response(summary)
            
        except ValueError:
            raise HTTPException(status_code=400, detail="日期格式无效，请使用YYYYMMDD格式")
        except Exception as e:
            logger.error(f"Failed to get metrics summary: {e}")
            raise HTTPException(status_code=500, detail="获取指标汇总失败")
    
    @router.get("/daily/{date}", response_model=DailyMetricsResponse)
    async def get_daily_metrics(
        date: str,
        k: int = Query(3, description="最大允许的codegen调用次数", ge=1, le=10)
    ):
        """获取单日指标"""
        try:
            # 验证日期格式
            datetime.strptime(date, "%Y%m%d")
            
            summary = service.get_daily_metrics(date, k)
            metrics = _summary_to_response(summary)
            
            return DailyMetricsResponse(date=date, metrics=metrics)
            
        except ValueError:
            raise HTTPException(status_code=400, detail="日期格式无效，请使用YYYYMMDD格式")
        except Exception as e:
            logger.error(f"Failed to get daily metrics for {date}: {e}")
            raise HTTPException(status_code=500, detail="获取日指标失败")
    
    @router.get("/weekly", response_model=PeriodMetricsResponse)
    async def get_weekly_metrics(
        end_date: str = Query(..., description="结束日期 (YYYYMMDD)", regex=r'^\d{8}$'),
        k: int = Query(3, description="最大允许的codegen调用次数", ge=1, le=10)
    ):
        """获取周指标（过去7天）"""
        try:
            end_dt = datetime.strptime(end_date, "%Y%m%d")
            start_dt = end_dt - timedelta(days=6)
            start_date = start_dt.strftime("%Y%m%d")
            
            summary = service.get_weekly_metrics(end_date, k)
            metrics = _summary_to_response(summary)
            
            return PeriodMetricsResponse(
                start_date=start_date,
                end_date=end_date,
                metrics=metrics
            )
            
        except ValueError:
            raise HTTPException(status_code=400, detail="日期格式无效，请使用YYYYMMDD格式")
        except Exception as e:
            logger.error(f"Failed to get weekly metrics ending {end_date}: {e}")
            raise HTTPException(status_code=500, detail="获取周指标失败")
    
    @router.get("/monthly/{year}/{month}", response_model=PeriodMetricsResponse)
    async def get_monthly_metrics(
        year: int,
        month: int,
        k: int = Query(3, description="最大允许的codegen调用次数", ge=1, le=10)
    ):
        """获取月指标"""
        try:
            if not (1 <= month <= 12):
                raise HTTPException(status_code=400, detail="月份必须在1-12之间")
            
            if not (2020 <= year <= 2030):
                raise HTTPException(status_code=400, detail="年份必须在2020-2030之间")
            
            summary = service.get_monthly_metrics(year, month, k)
            metrics = _summary_to_response(summary)
            
            # 计算月份的开始和结束日期
            start_date = f"{year:04d}{month:02d}01"
            if month == 12:
                next_month_start = datetime(year + 1, 1, 1)
            else:
                next_month_start = datetime(year, month + 1, 1)
            month_end = next_month_start - timedelta(days=1)
            end_date = month_end.strftime("%Y%m%d")
            
            return PeriodMetricsResponse(
                start_date=start_date,
                end_date=end_date,
                metrics=metrics
            )
            
        except Exception as e:
            logger.error(f"Failed to get monthly metrics for {year}-{month}: {e}")
            raise HTTPException(status_code=500, detail="获取月指标失败")
    
    @router.get("/user/{user_id}", response_model=MetricsResponse)
    async def get_user_metrics(
        user_id: str,
        start_date: str = Query(..., description="开始日期 (YYYYMMDD)", regex=r'^\d{8}$'),
        end_date: str = Query(..., description="结束日期 (YYYYMMDD)", regex=r'^\d{8}$'),
        k: int = Query(3, description="最大允许的codegen调用次数", ge=1, le=10)
    ):
        """获取特定用户的指标"""
        try:
            # 验证日期范围
            start_dt = datetime.strptime(start_date, "%Y%m%d")
            end_dt = datetime.strptime(end_date, "%Y%m%d")
            
            if start_dt > end_dt:
                raise HTTPException(status_code=400, detail="开始日期不能晚于结束日期")
            
            if (end_dt - start_dt).days > 365:
                raise HTTPException(status_code=400, detail="日期范围不能超过365天")
            
            summary = service.get_user_metrics(user_id, start_date, end_date, k)
            return _summary_to_response(summary)
            
        except ValueError:
            raise HTTPException(status_code=400, detail="日期格式无效，请使用YYYYMMDD格式")
        except Exception as e:
            logger.error(f"Failed to get user metrics for {user_id}: {e}")
            raise HTTPException(status_code=500, detail="获取用户指标失败")
    
    @router.post("/export/daily/{date}")
    async def export_daily_summary(date: str):
        """导出日常汇总"""
        try:
            # 验证日期格式
            datetime.strptime(date, "%Y%m%d")
            
            service.export_daily_summary(date)
            return {"message": f"日常汇总已导出: {date}"}
            
        except ValueError:
            raise HTTPException(status_code=400, detail="日期格式无效，请使用YYYYMMDD格式")
        except Exception as e:
            logger.error(f"Failed to export daily summary for {date}: {e}")
            raise HTTPException(status_code=500, detail="导出日常汇总失败")
    
    @router.get("/tasks")
    async def get_tasks_data(
        start_date: str = Query(..., description="开始日期 (YYYYMMDD)", regex=r'^\d{8}$'),
        end_date: str = Query(..., description="结束日期 (YYYYMMDD)", regex=r'^\d{8}$'),
        limit: int = Query(100, description="返回记录数限制", ge=1, le=1000)
    ):
        """获取任务原始数据"""
        try:
            # 验证日期范围
            start_dt = datetime.strptime(start_date, "%Y%m%d")
            end_dt = datetime.strptime(end_date, "%Y%m%d")
            
            if start_dt > end_dt:
                raise HTTPException(status_code=400, detail="开始日期不能晚于结束日期")
            
            if (end_dt - start_dt).days > 31:
                raise HTTPException(status_code=400, detail="日期范围不能超过31天")
            
            tasks = service.get_tasks_data(start_date, end_date)
            
            # 限制返回数据量
            if len(tasks) > limit:
                tasks = tasks[:limit]
            
            return {
                "total": len(tasks),
                "tasks": tasks
            }
            
        except ValueError:
            raise HTTPException(status_code=400, detail="日期格式无效，请使用YYYYMMDD格式")
        except Exception as e:
            logger.error(f"Failed to get tasks data: {e}")
            raise HTTPException(status_code=500, detail="获取任务数据失败")
    
    @router.get("/calls")
    async def get_calls_data(
        start_date: str = Query(..., description="开始日期 (YYYYMMDD)", regex=r'^\d{8}$'),
        end_date: str = Query(..., description="结束日期 (YYYYMMDD)", regex=r'^\d{8}$'),
        limit: int = Query(100, description="返回记录数限制", ge=1, le=1000)
    ):
        """获取调用原始数据"""
        try:
            # 验证日期范围
            start_dt = datetime.strptime(start_date, "%Y%m%d")
            end_dt = datetime.strptime(end_date, "%Y%m%d")
            
            if start_dt > end_dt:
                raise HTTPException(status_code=400, detail="开始日期不能晚于结束日期")
            
            if (end_dt - start_dt).days > 31:
                raise HTTPException(status_code=400, detail="日期范围不能超过31天")
            
            calls = service.get_calls_data(start_date, end_date)
            
            # 限制返回数据量
            if len(calls) > limit:
                calls = calls[:limit]
            
            return {
                "total": len(calls),
                "calls": calls
            }
            
        except ValueError:
            raise HTTPException(status_code=400, detail="日期格式无效，请使用YYYYMMDD格式")
        except Exception as e:
            logger.error(f"Failed to get calls data: {e}")
            raise HTTPException(status_code=500, detail="获取调用数据失败")
    
    return router