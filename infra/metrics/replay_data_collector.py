"""
离线复现数据收集器
用于收集完整的agent执行轨迹，支持离线环境完全复现用户交互场景
"""
import json
import os
import copy
import threading
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from threading import Lock

from common.logger.logger import logger
from .replay_data_models import (
    UserSessionReplay, AgentReasoningChain, CodegenContextReplay,
    CodeExecutionReplay, MCPCallChain, DataAccessTrace,
    EnvironmentSnapshot, TimeSeriesEvent,
    create_replay_data_id, get_current_timestamp, get_current_timestamp_ms,
    serialize_replay_data, get_environment_info
)
from .es_storage import ReplayDataESStorage


class ReplayDataCollector:
    """离线复现数据收集器"""
    
    def __init__(self, data_dir: str = "replay_data", enabled: bool = True, 
                 sampling_rate: float = 1.0, save_actual_data: bool = False,
                 es_storage: Optional[ReplayDataESStorage] = None):
        """
        初始化收集器
        
        Args:
            data_dir: 数据存储目录
            enabled: 是否启用数据收集
            sampling_rate: 采样率 (0.0-1.0)
            save_actual_data: 是否保存实际查询数据（隐私考虑）
            es_storage: ES存储实例
        """
        self.data_dir = Path(data_dir)
        self.enabled = enabled
        self.sampling_rate = sampling_rate
        self.save_actual_data = save_actual_data
        self.es_storage = es_storage
        self._lock = Lock()
        
        # 内存缓存，用于关联不同类型的数据
        self._session_cache: Dict[str, UserSessionReplay] = {}
        self._reasoning_cache: Dict[str, List[AgentReasoningChain]] = {}
        self._time_events_cache: Dict[str, List[TimeSeriesEvent]] = {}
        
        if self.enabled:
            self._ensure_directory_structure()
            logger.info(f"ReplayDataCollector initialized: {self.data_dir}, sampling: {sampling_rate}, ES storage: {es_storage is not None}")
        else:
            logger.info("ReplayDataCollector disabled")
    
    def _ensure_directory_structure(self):
        """确保目录结构存在"""
        subdirs = ['sessions', 'reasoning', 'codegen', 'execution', 'mcp', 'data_access', 'environment', 'time_series']
        for subdir in subdirs:
            (self.data_dir / subdir).mkdir(parents=True, exist_ok=True)
    
    def _should_sample(self) -> bool:
        """根据采样率决定是否收集数据"""
        import random
        return random.random() < self.sampling_rate
    
    def _get_date_str(self) -> str:
        """获取日期字符串"""
        return datetime.now().strftime("%Y%m%d")
    
    def _write_jsonl(self, subdir: str, filename: str, data: Dict[str, Any]) -> None:
        """写入JSONL文件"""
        if not self.enabled or not self._should_sample():
            return
        
        try:
            with self._lock:
                filepath = self.data_dir / subdir / filename
                with open(filepath, 'a', encoding='utf-8') as f:
                    # 确保数据按照JSONL格式写入：每行一个完整的JSON对象
                    json_line = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
                    f.write(json_line + "\n")
                    f.flush()  # 确保数据立即写入磁盘
                    
        except Exception as e:
            logger.error(f"Failed to write replay data to {subdir}/{filename}: {e}")
    
    def _save_to_es(self, data_type: str, data: Any) -> None:
        """保存数据到ES"""
        if not self.enabled or not self.es_storage or not self._should_sample():
            return
            
        try:
            if data_type == "session":
                self.es_storage.save_session_replay(data)
            elif data_type == "reasoning":
                self.es_storage.save_reasoning_chain(data)
            elif data_type == "codegen":
                self.es_storage.save_codegen_context(data)
            elif data_type == "execution":
                self.es_storage.save_execution_replay(data)
            elif data_type == "event":
                self.es_storage.save_time_series_event(data)
            else:
                logger.warning(f"Unknown data type for ES storage: {data_type}")
        except Exception as e:
            logger.error(f"Failed to save {data_type} data to ES: {e}")
    
    def _add_time_event(self, session_id: str, event_type: str, event_data: Dict[str, Any], 
                       related_ids: Optional[Dict[str, str]] = None) -> str:
        """添加时间序列事件"""
        event_id = create_replay_data_id()
        
        # 获取序列号
        if session_id not in self._time_events_cache:
            self._time_events_cache[session_id] = []
        sequence_number = len(self._time_events_cache[session_id])
        
        time_event = TimeSeriesEvent(
            event_id=event_id,
            session_id=session_id,
            timestamp_ms=get_current_timestamp_ms(),
            event_type=event_type,
            event_data=event_data,
            related_ids=related_ids or {},
            sequence_number=sequence_number
        )
        
        self._time_events_cache[session_id].append(time_event)
        
        # 写入文件
        date_str = self._get_date_str()
        filename = f"time_events_{date_str}.jsonl"
        self._write_jsonl("time_series", filename, serialize_replay_data(time_event))
        
        # 保存到ES
        self._save_to_es("event", time_event)
        
        return event_id
    
    # ==================== 用户会话记录 ====================
    
    def start_user_session(self, session_id: str, user_id: str, initial_query: str,
                          session_context: Optional[Dict[str, Any]] = None) -> None:
        """开始用户会话记录"""
        session_replay = UserSessionReplay(
            session_id=session_id,
            user_id=user_id,
            start_time=get_current_timestamp(),
            initial_query=initial_query,
            session_context=session_context or {}
        )
        
        self._session_cache[session_id] = session_replay
        
        # 添加时间事件
        self._add_time_event(session_id, "session_start", {
            "user_id": user_id,
            "initial_query": initial_query
        })
        
        logger.info(f"Started replay tracking for session: {session_id}")
    
    def add_conversation_turn(self, session_id: str, user_input: str, agent_response: str,
                            turn_metadata: Optional[Dict[str, Any]] = None) -> None:
        """添加对话轮次"""
        if session_id not in self._session_cache:
            logger.warning(f"Session {session_id} not found in cache")
            return
        
        session = self._session_cache[session_id]
        
        # 添加对话轮次
        turn_data = {
            "user_input": user_input,
            "agent_response": agent_response,
            "timestamp": get_current_timestamp(),
            "metadata": turn_metadata or {}
        }
        session.conversation_turns.append(turn_data)
        session.user_inputs.append(user_input)
        session.agent_responses.append(agent_response)
        
        # 添加时间事件
        self._add_time_event(session_id, "conversation_turn", turn_data)
    
    def add_user_feedback(self, session_id: str, feedback: str, feedback_type: str = "general") -> None:
        """添加用户反馈"""
        if session_id not in self._session_cache:
            return
        
        session = self._session_cache[session_id]
        session.user_feedback.append(feedback)
        
        # 添加时间事件
        self._add_time_event(session_id, "user_feedback", {
            "feedback": feedback,
            "feedback_type": feedback_type
        })
    
    def finish_user_session(self, session_id: str, final_satisfaction: Optional[int] = None) -> None:
        """完成用户会话记录"""
        if session_id not in self._session_cache:
            return
        
        session = self._session_cache[session_id]
        session.end_time = get_current_timestamp()
        session.final_satisfaction = final_satisfaction
        
        # 写入文件
        date_str = self._get_date_str()
        filename = f"session_{session_id}_{date_str}.jsonl"
        self._write_jsonl("sessions", filename, serialize_replay_data(session))
        
        # 保存到ES
        self._save_to_es("session", session)
        
        # 添加时间事件
        self._add_time_event(session_id, "session_end", {
            "final_satisfaction": final_satisfaction,
            "total_turns": len(session.conversation_turns)
        })
        
        # 清理缓存
        if session_id in self._session_cache:
            del self._session_cache[session_id]
        
        logger.info(f"Finished replay tracking for session: {session_id}")
    
    # ==================== Agent推理过程记录 ====================
    
    def record_agent_reasoning(self, session_id: str, task_id: str, subtask_sequence: int,
                             agent_state_before: Dict[str, Any], agent_state_after: Dict[str, Any],
                             tool_selection_reasoning: str = "", plan_generation_process: Optional[Dict[str, Any]] = None,
                             execution_strategy: str = "", graph_node: str = "",
                             graph_edge_condition: Optional[Dict[str, Any]] = None,
                             graph_state_transition: Optional[Dict[str, Any]] = None) -> str:
        """记录Agent推理过程"""
        reasoning_id = create_replay_data_id()
        
        reasoning = AgentReasoningChain(
            reasoning_id=reasoning_id,
            session_id=session_id,
            task_id=task_id,
            subtask_sequence=subtask_sequence,
            timestamp=get_current_timestamp(),
            agent_state_before=copy.deepcopy(agent_state_before),
            agent_state_after=copy.deepcopy(agent_state_after),
            tool_selection_reasoning=tool_selection_reasoning,
            plan_generation_process=plan_generation_process or {},
            execution_strategy=execution_strategy,
            graph_node=graph_node,
            graph_edge_condition=graph_edge_condition or {},
            graph_state_transition=graph_state_transition or {}
        )
        
        # 缓存推理链
        if session_id not in self._reasoning_cache:
            self._reasoning_cache[session_id] = []
        self._reasoning_cache[session_id].append(reasoning)
        
        # 写入文件
        date_str = self._get_date_str()
        filename = f"reasoning_{date_str}.jsonl"
        self._write_jsonl("reasoning", filename, serialize_replay_data(reasoning))
        
        # 保存到ES
        self._save_to_es("reasoning", reasoning)
        
        # 添加时间事件
        self._add_time_event(session_id, "agent_reasoning", {
            "reasoning_id": reasoning_id,
            "task_id": task_id,
            "graph_node": graph_node
        }, {"task_id": task_id, "reasoning_id": reasoning_id})
        
        return reasoning_id
    
    # ==================== 代码生成上下文记录 ====================
    
    def record_codegen_context(self, task_id: str, call_id: str, call_sequence: int,
                             full_prompt: str, model_name: str, session_id: str = "",
                             model_parameters: Optional[Dict[str, Any]] = None,
                             previous_code_history: Optional[List[str]] = None,
                             execution_environment_vars: Optional[Dict[str, Any]] = None,
                             available_tools: Optional[List[Dict[str, Any]]] = None,
                             data_schema_info: Optional[Dict[str, Any]] = None,
                             generated_code: str = "", generation_reasoning: str = "",
                             confidence_score: Optional[float] = None,
                             action_manager_state: Optional[Dict[str, Any]] = None,
                             scenario_detection_result: Optional[Dict[str, Any]] = None,
                             library_config_applied: Optional[List[str]] = None) -> str:
        """记录代码生成完整上下文"""
        context_id = create_replay_data_id()
        
        context = CodegenContextReplay(
            context_id=context_id,
            task_id=task_id,
            call_id=call_id,
            call_sequence=call_sequence,
            timestamp=get_current_timestamp(),
            session_id=session_id,
            full_prompt=full_prompt,
            model_name=model_name,
            model_parameters=model_parameters or {},
            previous_code_history=previous_code_history or [],
            execution_environment_vars=execution_environment_vars or {},
            available_tools=available_tools or [],
            data_schema_info=data_schema_info or {},
            generated_code=generated_code,
            generation_reasoning=generation_reasoning,
            confidence_score=confidence_score,
            action_manager_state=action_manager_state or {},
            scenario_detection_result=scenario_detection_result or {},
            library_config_applied=library_config_applied or []
        )
        
        # 写入文件
        date_str = self._get_date_str()
        filename = f"codegen_context_{date_str}.jsonl"
        self._write_jsonl("codegen", filename, serialize_replay_data(context))
        
        # 保存到ES
        self._save_to_es("codegen", context)
        
        return context_id
    
    # ==================== 代码执行记录 ====================
    
    def record_code_execution(self, task_id: str, call_id: str, code_before_execution: str,
                            environment_snapshot_before: Optional[Dict[str, Any]] = None,
                            available_variables: Optional[Dict[str, str]] = None,
                            imported_modules: Optional[List[str]] = None,
                            execution_method: str = "jupyter", execution_timeout: Optional[int] = None,
                            execution_success: bool = False, execution_output: str = "",
                            execution_errors: str = "", execution_time_ms: Optional[int] = None,
                            memory_usage_mb: Optional[float] = None,
                            environment_changes: Optional[Dict[str, Any]] = None,
                            new_variables_created: Optional[Dict[str, str]] = None,
                            files_created: Optional[List[str]] = None,
                            visualizations_generated: Optional[List[str]] = None) -> str:
        """记录代码执行过程"""
        execution_id = create_replay_data_id()
        
        execution = CodeExecutionReplay(
            execution_id=execution_id,
            task_id=task_id,
            call_id=call_id,
            timestamp=get_current_timestamp(),
            code_before_execution=code_before_execution,
            environment_snapshot_before=environment_snapshot_before or {},
            available_variables=available_variables or {},
            imported_modules=imported_modules or [],
            execution_method=execution_method,
            execution_timeout=execution_timeout,
            execution_success=execution_success,
            execution_output=execution_output,
            execution_errors=execution_errors,
            execution_time_ms=execution_time_ms,
            memory_usage_mb=memory_usage_mb,
            environment_changes=environment_changes or {},
            new_variables_created=new_variables_created or {},
            files_created=files_created or [],
            visualizations_generated=visualizations_generated or []
        )
        
        # 写入文件
        date_str = self._get_date_str()
        filename = f"code_execution_{date_str}.jsonl"
        self._write_jsonl("execution", filename, serialize_replay_data(execution))
        
        # 保存到ES
        self._save_to_es("execution", execution)
        
        return execution_id
    
    # ==================== MCP工具调用链记录 ====================
    
    def record_mcp_call_chain(self, task_id: str, mcp_session_id: str,
                            server_info: Optional[Dict[str, Any]] = None,
                            connection_config: Optional[Dict[str, Any]] = None,
                            tool_calls_sequence: Optional[List[Dict[str, Any]]] = None,
                            call_details: Optional[List[Dict[str, Any]]] = None,
                            tool_state_changes: Optional[Dict[str, Any]] = None) -> str:
        """记录MCP工具调用链"""
        call_chain_id = create_replay_data_id()
        
        mcp_chain = MCPCallChain(
            mcp_session_id=mcp_session_id,
            task_id=task_id,
            call_chain_id=call_chain_id,
            timestamp=get_current_timestamp(),
            server_info=server_info or {},
            connection_config=connection_config or {},
            tool_calls_sequence=tool_calls_sequence or [],
            call_details=call_details or [],
            tool_state_changes=tool_state_changes or {}
        )
        
        # 写入文件
        date_str = self._get_date_str()
        filename = f"mcp_calls_{date_str}.jsonl"
        self._write_jsonl("mcp", filename, serialize_replay_data(mcp_chain))
        
        return call_chain_id
    
    # ==================== 数据访问轨迹记录 ====================
    
    def record_data_access(self, task_id: str, data_source_type: str,
                         data_source_config: Optional[Dict[str, Any]] = None,
                         connection_info: Optional[Dict[str, Any]] = None,
                         sql_queries: Optional[List[str]] = None,
                         query_parameters: Optional[List[Dict[str, Any]]] = None,
                         query_results_schema: Optional[List[Dict[str, Any]]] = None,
                         query_results_sample: Optional[List[Dict[str, Any]]] = None,
                         result_row_counts: Optional[List[int]] = None,
                         schema_discovery_process: Optional[Dict[str, Any]] = None,
                         table_analysis_results: Optional[Dict[str, Any]] = None) -> str:
        """记录数据访问轨迹"""
        trace_id = create_replay_data_id()
        
        # 根据配置决定是否保存实际数据
        if not self.save_actual_data:
            query_results_sample = []  # 不保存实际查询结果
        
        trace = DataAccessTrace(
            trace_id=trace_id,
            task_id=task_id,
            timestamp=get_current_timestamp(),
            data_source_type=data_source_type,
            data_source_config=data_source_config or {},
            connection_info=connection_info or {},
            sql_queries=sql_queries or [],
            query_parameters=query_parameters or [],
            query_results_schema=query_results_schema or [],
            query_results_sample=query_results_sample or [],
            result_row_counts=result_row_counts or [],
            schema_discovery_process=schema_discovery_process or {},
            table_analysis_results=table_analysis_results or {}
        )
        
        # 写入文件
        date_str = self._get_date_str()
        filename = f"data_access_{date_str}.jsonl"
        self._write_jsonl("data_access", filename, serialize_replay_data(trace))
        
        return trace_id
    
    # ==================== 环境快照记录 ====================
    
    def record_environment_snapshot(self, session_id: str, app_config_snapshot: Optional[Dict[str, Any]] = None,
                                  feature_flags: Optional[Dict[str, bool]] = None,
                                  model_configs: Optional[Dict[str, Any]] = None,
                                  environment_variables: Optional[Dict[str, str]] = None) -> str:
        """记录环境快照"""
        snapshot_id = create_replay_data_id()
        
        # 获取系统环境信息
        env_info = get_environment_info()
        
        snapshot = EnvironmentSnapshot(
            snapshot_id=snapshot_id,
            session_id=session_id,
            timestamp=get_current_timestamp(),
            python_version=env_info["python_version"],
            system_platform=env_info["system_platform"],
            installed_packages=env_info["installed_packages"],
            app_config_snapshot=app_config_snapshot or {},
            feature_flags=feature_flags or {},
            model_configs=model_configs or {},
            environment_variables=environment_variables or {},
            working_directory=env_info["working_directory"]
        )
        
        # 写入文件
        date_str = self._get_date_str()
        filename = f"environment_{date_str}.jsonl"
        self._write_jsonl("environment", filename, serialize_replay_data(snapshot))
        
        return snapshot_id
    
    # ==================== 实用方法 ====================
    
    def flush_session_cache(self, session_id: Optional[str] = None) -> None:
        """刷新会话缓存到文件"""
        if session_id:
            if session_id in self._session_cache:
                self.finish_user_session(session_id)
        else:
            # 刷新所有缓存
            for sid in list(self._session_cache.keys()):
                self.finish_user_session(sid)
    
    def get_session_stats(self) -> Dict[str, Any]:
        """获取收集器统计信息"""
        return {
            "enabled": self.enabled,
            "sampling_rate": self.sampling_rate,
            "data_dir": str(self.data_dir),
            "cached_sessions": len(self._session_cache),
            "cached_reasoning_chains": sum(len(chains) for chains in self._reasoning_cache.values()),
            "cached_time_events": sum(len(events) for events in self._time_events_cache.values())
        }


# 全局实例
_replay_collector_instance: Optional[ReplayDataCollector] = None


def get_replay_collector() -> ReplayDataCollector:
    """获取全局replay collector实例"""
    global _replay_collector_instance
    if _replay_collector_instance is None:
        # 从配置中读取设置
        from common.share.config import appConfig
        
        try:
            enabled = appConfig.metrics.replay.enabled
            data_dir = appConfig.metrics.replay.data_dir
            sampling_rate = appConfig.metrics.replay.sampling_rate
            save_actual_data = appConfig.metrics.replay.save_actual_data
        except AttributeError:
            # 如果配置不存在，使用默认值
            enabled = True
            data_dir = 'replay_data'
            sampling_rate = 1.0
            save_actual_data = False
        
        # 初始化ES存储
        es_storage = None
        try:
            es_config = appConfig.automic.nl2sql.es
            app_id = appConfig.app_id or "default"
            es_storage = ReplayDataESStorage(es_config, app_id)
        except AttributeError:
            logger.warning("ES configuration not found, ES storage disabled for replay data")
        except Exception as e:
            logger.error(f"Failed to initialize ES storage for replay data: {e}")
        
        _replay_collector_instance = ReplayDataCollector(
            data_dir=data_dir, 
            enabled=enabled,
            sampling_rate=sampling_rate,
            save_actual_data=save_actual_data,
            es_storage=es_storage
        )
    
    return _replay_collector_instance