"""
环境快照和状态记录器
用于记录系统环境、配置状态，支持离线复现时的环境重建
"""
import os
import sys
import time
import json
import subprocess
from typing import Dict, Any, Optional, List
from pathlib import Path

from common.logger.logger import logger
from .replay_data_collector import get_replay_collector


class EnvironmentRecorder:
    """环境快照记录器"""
    
    def __init__(self):
        self.replay_collector = get_replay_collector()
    
    def record_session_environment(self, session_id: str) -> str:
        """记录会话环境快照"""
        try:
            # 获取应用配置快照
            app_config = self._get_app_config_snapshot()
            
            # 获取特性开关状态
            feature_flags = self._get_feature_flags()
            
            # 获取模型配置
            model_configs = self._get_model_configs()
            
            # 获取环境变量
            env_vars = self._get_environment_variables()
            
            # 记录环境快照
            snapshot_id = self.replay_collector.record_environment_snapshot(
                session_id=session_id,
                app_config_snapshot=app_config,
                feature_flags=feature_flags,
                model_configs=model_configs,
                environment_variables=env_vars
            )
            
            logger.info(f"📊 Recorded environment snapshot: {snapshot_id} for session: {session_id}")
            return snapshot_id
            
        except Exception as e:
            logger.error(f"Failed to record environment snapshot: {e}")
            return ""
    
    def _get_app_config_snapshot(self) -> Dict[str, Any]:
        """获取应用配置快照"""
        try:
            from common.share.config import appConfig
            
            # 提取关键配置信息（去除敏感信息）
            config_snapshot = {}
            
            # Common配置
            if hasattr(appConfig, 'common'):
                config_snapshot['common'] = {
                    'llm': {
                        'model_name': getattr(appConfig.common.llm, 'model_name', ''),
                        'temperature': getattr(appConfig.common.llm, 'temperature', 0.7),
                        'base_url': getattr(appConfig.common.llm, 'base_url', '').split('/')[-1] if getattr(appConfig.common.llm, 'base_url', '') else ''
                    },
                    'llm_embedding': {
                        'model_name': getattr(appConfig.common.llm_embedding, 'model_name', ''),
                        'model_dims': getattr(appConfig.common.llm_embedding, 'model_dims', 0)
                    }
                }
            
            # Metrics配置
            if hasattr(appConfig, 'metrics'):
                config_snapshot['metrics'] = {
                    'codegen': {
                        'enabled': getattr(appConfig.metrics.codegen, 'enabled', True),
                        'k_values': getattr(appConfig.metrics.codegen, 'k_values', [1, 2, 3, 5]),
                        'track_token_usage': getattr(appConfig.metrics.codegen, 'track_token_usage', True)
                    }
                }
                
                # Replay配置
                if hasattr(appConfig.metrics, 'replay'):
                    config_snapshot['metrics']['replay'] = {
                        'enabled': getattr(appConfig.metrics.replay, 'enabled', True),
                        'sampling_rate': getattr(appConfig.metrics.replay, 'sampling_rate', 1.0),
                        'save_actual_data': getattr(appConfig.metrics.replay, 'save_actual_data', False)
                    }
            
            return config_snapshot
            
        except Exception as e:
            logger.warning(f"Failed to get app config snapshot: {e}")
            return {}
    
    def _get_feature_flags(self) -> Dict[str, bool]:
        """获取特性开关状态"""
        try:
            # 可以从配置文件或环境变量中读取特性开关
            feature_flags = {}
            
            # 从环境变量中读取特性开关
            for key, value in os.environ.items():
                if key.startswith('FEATURE_') or key.startswith('ENABLE_'):
                    feature_flags[key] = value.lower() in ('true', '1', 'yes', 'on')
            
            # 添加一些常见的特性开关
            feature_flags.update({
                'LANGSMITH_TRACING': os.getenv('LANGSMITH_TRACING', 'false').lower() == 'true',
                'MEM0_TELEMETRY': os.getenv('MEM0_TELEMETRY', 'true').lower() == 'true',
                'DEBUG_MODE': os.getenv('DEBUG', 'false').lower() == 'true'
            })
            
            return feature_flags
            
        except Exception as e:
            logger.warning(f"Failed to get feature flags: {e}")
            return {}
    
    def _get_model_configs(self) -> Dict[str, Any]:
        """获取模型配置信息"""
        try:
            from common.share.config import appConfig
            
            model_configs = {}
            
            # LLM配置
            if hasattr(appConfig, 'common') and hasattr(appConfig.common, 'llm'):
                model_configs['primary_llm'] = {
                    'model_name': getattr(appConfig.common.llm, 'model_name', ''),
                    'temperature': getattr(appConfig.common.llm, 'temperature', 0.7),
                    'api_endpoint': 'configured'  # 不记录实际endpoint
                }
            
            # NL2Code配置
            if hasattr(appConfig, 'automic') and hasattr(appConfig.automic, 'nl2code'):
                model_configs['nl2code'] = {
                    'model_name': getattr(appConfig.automic.nl2code.llm, 'model_name', ''),
                    'temperature': getattr(appConfig.automic.nl2code.llm, 'temperature', 0.1)
                }
            
            # NL2SQL配置
            if hasattr(appConfig, 'automic') and hasattr(appConfig.automic, 'nl2sql'):
                model_configs['nl2sql'] = {
                    'model_name': getattr(appConfig.automic.nl2sql.llm, 'model_name', ''),
                    'embedding_model': getattr(appConfig.automic.nl2sql.embedding, 'model_name', '')
                }
            
            return model_configs
            
        except Exception as e:
            logger.warning(f"Failed to get model configs: {e}")
            return {}
    
    def _get_environment_variables(self) -> Dict[str, str]:
        """获取相关环境变量（去除敏感信息）"""
        try:
            # 只记录非敏感的环境变量
            safe_env_vars = {}
            
            # 安全的环境变量列表
            safe_keys = [
                'PYTHONPATH', 'PATH', 'SHELL', 'TERM', 'USER', 'HOME',
                'PWD', 'LANG', 'LC_ALL', 'TZ', 'HOSTNAME',
                'OTEL_EXPORTER_OTLP_ENDPOINT', 'LANGSMITH_TRACING', 'MEM0_TELEMETRY',
                'DEBUG', 'ENV', 'ENVIRONMENT'
            ]
            
            for key in safe_keys:
                if key in os.environ:
                    safe_env_vars[key] = os.environ[key]
            
            # 添加Python相关信息
            safe_env_vars.update({
                'PYTHON_VERSION': sys.version,
                'PYTHON_EXECUTABLE': sys.executable,
                'PLATFORM': sys.platform
            })
            
            return safe_env_vars
            
        except Exception as e:
            logger.warning(f"Failed to get environment variables: {e}")
            return {}
    
    def record_runtime_state(self, session_id: str, additional_state: Optional[Dict[str, Any]] = None) -> str:
        """记录运行时状态"""
        try:
            # 获取系统资源状态
            runtime_state = self._get_runtime_state()
            
            # 合并额外状态
            if additional_state:
                runtime_state.update(additional_state)
            
            # 生成状态记录ID
            state_id = f"runtime_{session_id}_{int(time.time())}"
            
            # 将运行时状态作为环境快照的一部分记录
            snapshot_id = self.replay_collector.record_environment_snapshot(
                session_id=session_id,
                app_config_snapshot={'runtime_state': runtime_state},
                feature_flags={},
                model_configs={},
                environment_variables={}
            )
            
            logger.info(f"📊 Recorded runtime state: {state_id}")
            return snapshot_id
            
        except Exception as e:
            logger.error(f"Failed to record runtime state: {e}")
            return ""
    
    def _get_runtime_state(self) -> Dict[str, Any]:
        """获取运行时状态信息"""
        try:
            runtime_state = {}
            
            # 获取内存使用情况
            try:
                import psutil
                process = psutil.Process()
                runtime_state['memory'] = {
                    'rss_mb': process.memory_info().rss / 1024 / 1024,
                    'vms_mb': process.memory_info().vms / 1024 / 1024,
                    'cpu_percent': process.cpu_percent()
                }
            except ImportError:
                runtime_state['memory'] = {'note': 'psutil not available'}
            
            # 获取当前工作目录
            runtime_state['working_directory'] = os.getcwd()
            
            # 获取运行时间
            runtime_state['timestamp'] = time.time()
            runtime_state['timestamp_iso'] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
            
            return runtime_state
            
        except Exception as e:
            logger.warning(f"Failed to get runtime state: {e}")
            return {}
    
    def export_environment_summary(self, session_id: str, output_file: Optional[str] = None) -> str:
        """导出环境摘要到文件"""
        try:
            # 生成环境摘要
            summary = {
                'session_id': session_id,
                'export_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'environment': {
                    'app_config': self._get_app_config_snapshot(),
                    'feature_flags': self._get_feature_flags(),
                    'model_configs': self._get_model_configs(),
                    'runtime_state': self._get_runtime_state()
                }
            }
            
            # 确定输出文件路径
            if not output_file:
                output_file = f"environment_summary_{session_id}_{int(time.time())}.json"
            
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📊 Exported environment summary to: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Failed to export environment summary: {e}")
            return ""


# 全局实例
_environment_recorder_instance: Optional[EnvironmentRecorder] = None


def get_environment_recorder() -> EnvironmentRecorder:
    """获取全局environment recorder实例"""
    global _environment_recorder_instance
    if _environment_recorder_instance is None:
        _environment_recorder_instance = EnvironmentRecorder()
    return _environment_recorder_instance


def record_session_environment(session_id: str) -> str:
    """便捷函数：记录会话环境"""
    recorder = get_environment_recorder()
    return recorder.record_session_environment(session_id)


def record_runtime_state(session_id: str, additional_state: Optional[Dict[str, Any]] = None) -> str:
    """便捷函数：记录运行时状态"""
    recorder = get_environment_recorder()
    return recorder.record_runtime_state(session_id, additional_state)