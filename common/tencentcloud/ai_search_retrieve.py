import threading
from typing import Dict, List, Any

from langid import langid
from tencentcloud.common import credential
from tencentcloud.common.common_client import CommonClient
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile

from common.logger.logger import logger
from common.share.config import appConfig
from infra.domain.user_search_config_entity import UserSearchConfig
from infra.memory.knowledge_operator import KnowledgeOperator

# Python版本：3.9及以上
# pip install tencentcloud-sdk-python 首次使用需要下载
# ES集群版本：8.16及以上
# pip install elasticsearch
# 模型相关配置
EMBEDDING_MODEL_NAME = "bge-m3"
RERANK_MODEL_NAME = "bge-reranker-v2-m3"

_tencent_cloud_client = None
_client_lock = threading.Lock()  # 用于线程安全的锁

def get_tencent_cloud_client() -> CommonClient:
    """获取或初始化腾讯云API客户端（线程安全）"""
    global _tencent_cloud_client

    # 双重检查锁定模式
    if _tencent_cloud_client is None:
        with _client_lock:
            # 再次检查，避免在等待锁的过程中其他线程已经初始化
            if _tencent_cloud_client is None:
                access = appConfig.automic.aisearch.access_info
                SECRET_ID = access.secret_id
                SECRET_KEY = access.secret_key
                TENCENT_CLOUD_ENDPOINT = access.endpoint
                SERVICE = access.service
                VERSION = access.api_version
                REGION = access.region

                logger.info("Initializing Tencent Cloud client...")
                try:
                    credentials = credential.Credential(SECRET_ID, SECRET_KEY)
                    http_profile = HttpProfile()
                    http_profile.endpoint = TENCENT_CLOUD_ENDPOINT
                    client_profile = ClientProfile()
                    client_profile.httpProfile = http_profile
                    _tencent_cloud_client = CommonClient(
                        SERVICE, VERSION, credentials, REGION, profile=client_profile
                    )
                    logger.info("Tencent Cloud client initialized successfully")
                except Exception as e:
                    logger.error(f"Failed to initialize Tencent Cloud client: {str(e)}")
                    raise

    return _tencent_cloud_client

def get_text_embeddings(query: str) -> List[Dict]:
    """查询文本向量化

    Args:
        client: 腾讯云客户端实例
        query: 需要向量化的文本

    Returns:
        List[Dict]: 包含向量信息的字典列表，每个字典包含content和content_embedding字段
                   如果出错则返回空列表
    """
    params = {
        "ModelName": EMBEDDING_MODEL_NAME,
        "Texts": [query],
    }

    try:
        tencent_cloud_client = get_tencent_cloud_client()  # 获取客户端实例
        response = tencent_cloud_client.call_json("GetTextEmbedding", params)
        data_list = response['Response']['Data']

        if not data_list:
            logger.info("No embeddings generated for the input text")
            return []

        return [{
            "content_embedding": data['Embedding'],
            "content": query
        } for data in data_list]

    except Exception as e:
        logger.error(f"Error occurred while getting embeddings: {e}")
        raise

def identify_es_analyzer(text):
    """
    根据用户输入的内容，选择使用的es分词器
    :param text: 用户输入的内容
    :return: 分词器名称
    """
    try:
        language = langid.classify(text)[0]
        return "ik_smart" if language == 'zh' else "standard"
    except Exception as e:
        logger.warn(f"Error identifying es analyzer for text: {text}. Exception: {e}")
        return "standard"

def _build_hybrid_query(user_query: str, embedding: List[float], recall_num: int, embedding_weight: float, knowledge_base_ids: List[str]) -> Dict:
    """构建混合搜索DSL"""
    return {
        "query": {
            "bool": {
                "should": [
                    {
                        "match": {
                            "content": {
                                "query": user_query,
                                "boost": 1 - embedding_weight,
                                "analyzer": identify_es_analyzer(user_query)
                            }
                        }
                    },
                    {
                        "knn": {
                            "field": "content_embedding",
                            "query_vector": embedding,
                            "k": recall_num,
                            "num_candidates": recall_num * 2,
                            "boost": embedding_weight
                        }
                    }
                ],
                "filter": [{
                    "terms": {"knowledge_base_id": knowledge_base_ids}
                }]
            }
        },
        "size": recall_num
    }


def _build_hybrid_query_rrf(user_query: str, embedding: List[float], recall_num: int, embedding_weight: float, knowledge_base_ids: List[str]) -> Dict:
    return {
        "retriever": {
            "rank_fusion": {
                "retrievers": [
                    {
                        "standard": {
                            "query": {
                                "bool":{
                                    "must": [{
                                        "match": {
                                            "content": {
                                                "query": user_query,
                                                "boost": 1 - embedding_weight,
                                                "analyzer": identify_es_analyzer(user_query)
                                            }
                                        }
                                    }],
                                    "filter": [{
                                        "terms": {"knowledge_base_id": knowledge_base_ids}
                                    }]
                                }
                            }
                        }
                    },
                    {
                        "knn": {
                            "field": "content_embedding",
                            "query_vector": embedding,
                            "k": recall_num,
                            "num_candidates": recall_num * 2,
                            "filter": {
                                "terms": {"knowledge_base_id": knowledge_base_ids}
                            }
                        }
                    }
                ],
                "weights": [1 - embedding_weight, embedding_weight],  # 文本与向量的权重可以通过"weights"设置，可根据具体业务数据调试，取效果最优的权重即可。
                "rank_constant": 60,  # RRF 平滑因子（默认值）
                "rank_window_size": 200  # 需覆盖原查询的 size 值
            }
        },
        "size": recall_num
    }


def _build_vector_query(embedding: List[float], recall_num: int, knowledge_base_ids: List[str]) -> Dict:
    """构建向量搜索DSL"""
    return {
        "query": {
            "knn": {
                "field": "content_embedding",
                "query_vector": embedding,
                "k": recall_num,
                "num_candidates": recall_num * 2,
                "filter": {
                    "terms": {
                        "knowledge_base_id": knowledge_base_ids
                    }
                }
            }
        },
        "size": recall_num
    }


def _build_text_query(user_query: str, recall_num: int, knowledge_base_ids: List[str]) -> Dict:
    """构建全文搜索DSL"""
    return {
        "query": {
            "bool": {
                "must": [{
                    "match": {
                        "content":
                            {
                                "query": user_query,
                                "analyzer": identify_es_analyzer(user_query)
                            }}
                }],
                "filter": [{
                    "terms": {"knowledge_base_id": knowledge_base_ids}
                }]
            }
        },
        "size": recall_num
    }


def search_elasticsearch(
        app_id: str,
        embeddings: List[Dict],
        index: str,
        user_query: str,
        user_config: UserSearchConfig,
        knowledge_base_ids: List[str]
) -> List[str]:
    """
    优化后的Elasticsearch搜索方法

    Args:
        app_id: 应用ID
        embeddings: 包含向量信息的列表
        index: 索引名称
        user_query: 用户查询文本
        user_config: 用户搜索配置模型

    Returns:
        检索到的文档内容列表

    """
    try:
        # 获取配置参数
        search_type = user_config.search_type
        recall_num = user_config.recall_num
        weight = user_config.embedding_weight

        # 构建对应类型的查询DSL
        if search_type == 0:  # 混合搜索
            embedding = embeddings[0].get("content_embedding")
            query = _build_hybrid_query(user_query, embedding, recall_num, weight, knowledge_base_ids)
        elif search_type == 1:  # 向量搜索
            embedding = embeddings[0].get("content_embedding")
            query = _build_vector_query(embedding, recall_num, knowledge_base_ids)
        else:  # 全文搜索
            query = _build_text_query(user_query, recall_num, knowledge_base_ids)
        logger.info(f"ai search query by es: {query}")
        knowledge_operator = KnowledgeOperator.get_instance(app_id)
        # 执行搜索
        index = f"{index}_{app_id}"
        response = knowledge_operator.sync_client.search(index=index, body=query)
        return [hit['_source']['content'] for hit in response['hits']['hits']] or []

    except ValueError as e:
        logger.warning(f"Invalid search config: {e}")
        return []
    except Exception as e:
        logger.error(f"Search failed: {e}")
        return []

def search_elasticsearch_by_score(
        app_id: str,
        embeddings: List[Dict],
        index: str,
        user_query: str,
        user_config: UserSearchConfig,
        knowledge_base_ids: List[str],
) -> List[Dict[str, Any]]:
    """
    优化后的Elasticsearch搜索方法

    Args:
        app_id: 应用ID
        embeddings: 包含向量信息的列表
        index: 索引名称
        user_query: 用户查询文本
        user_config: 用户搜索配置模型

    Returns:
        检索到的文档内容列表

    """

    try:
        # 获取配置参数
        search_type = user_config.search_type
        recall_num = user_config.recall_num
        weight = user_config.embedding_weight

        # 构建对应类型的查询DSL

        if search_type == 0:  # 混合搜索
            embedding = embeddings[0].get("content_embedding")
            query = _build_hybrid_query_rrf(user_query, embedding, recall_num, weight, knowledge_base_ids)
        elif search_type == 1:  # 向量搜索
            embedding = embeddings[0].get("content_embedding")
            query = _build_vector_query(embedding, recall_num, knowledge_base_ids)
        else:  # 全文搜索
            query = _build_text_query(user_query, recall_num, knowledge_base_ids)
        logger.info(f"ai search query by es: {query}")
        knowledge_operator = KnowledgeOperator.get_instance(app_id)
        # 执行搜索
        index = f"{index}_{app_id}"
        response = knowledge_operator.sync_client.search(index=index, body=query)
        return [{"content": hit['_source']['content'],
                 "file_id": hit['_source']['file_id'],
                 "knowledge_base_id": hit['_source']['knowledge_base_id'],
                 "chunk_id": hit['_source']['chunk_id'],
                 "score": hit['_score']} for hit in response['hits']['hits']] or []

    except ValueError as e:
        logger.warning(f"Invalid search config: {e}")
        return []
    except Exception as e:
        logger.error(f"Search failed: {e}")
        return []


def rerank_documents_by_score(query: str, docs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """召回内容重排序

    Args:
        query: 用户查询
        docs: 待重排序的文档列表

    Returns:
        List[Dict[str,Any]]: 重排序后的文档列表，包含原始文档数据和新的rerank_score字段
    """
    if not docs:
        logger.info("文档列表为空，无需重排序。")
        return []

    # 直接提取文档内容，无需单独的 truncated_docs 列表，因为原始 docs 列表就是我们需要的
    document_contents = [doc['content'] for doc in docs]

    json_object = {
        "ModelName": RERANK_MODEL_NAME,
        "Query": query,
        "Documents": document_contents
    }

    try:
        tencent_cloud_client = get_tencent_cloud_client()  # 获取客户端实例
        response = tencent_cloud_client.call_json("RunRerank", json_object)
        reranked_data = response.get('Response', {}).get('Data')

        if not reranked_data:
            logger.info("重排序服务未返回任何文档数据。")
            return []

        # 使用列表推导式和字典合并，更简洁地构建结果
        # 遍历 reranked_data，根据 Index 获取原始文档，并添加 rerank_score
        result = []
        for item in reranked_data:
            index = item.get('Index')
            original_doc = docs[index].copy()  # 复制原始文档，避免修改原对象
            original_doc['rerank_score'] = item.get('RelevanceScore', 0.0)
            result.append(original_doc)
        return result
    except Exception as e:
        logger.error(f"Error in reranking: {str(e)}")
        return []

def rerank_documents(query: str, docs: List[str]) -> List[str]:
    """召回内容重排序

    Args:
        client: 腾讯云客户端实例
        query: 用户查询
        docs: 待重排序的文档列表

    Returns:
        List[str]: 重排序后的文档列表，如果出错则返回空列表
    """
    json_object = {
        "ModelName": RERANK_MODEL_NAME,
        "Query": query,
        # "ReturnDocuments": True,
        "Documents": docs
    }
    try:
        tencent_cloud_client = get_tencent_cloud_client()  # 获取客户端实例
        response = tencent_cloud_client.call_json("RunRerank", json_object)
        reranked_data = response['Response']['Data']
        if not reranked_data:
            logger.info("No documents returned from reranking")
            return []
        return [item['Document'] for item in reranked_data]

    except Exception as e:
        logger.error(f"Error occurred while reranking documents: {e}")
        raise e

if __name__ == "__main__":
    try:
        user_query = "模型"
        # Step 1: 将query内容进行文本向量化
        print(f"\nStep 1: Processing query - {user_query}")
        embedding_list = get_text_embeddings(user_query)
        if not embedding_list:
            raise ValueError("No embeddings generated for the query")
        INDEX_NAME = "knowledge-base"
        # Step 2: 根据向量化的结果查询搜索引擎
        print("\nStep 2: Query search engine based on embedding list...")
        doc_list = search_elasticsearch_by_score("test1", embedding_list, INDEX_NAME, user_query,
                                        UserSearchConfig(
                                            app_id="test1",
                                            search_type=0,
                                            recall_num=5,
                                            embedding_weight=0.5,
                                            rerank_status=0,
                                        ), ["default"])
        if not doc_list:
            raise ValueError("No documents found for Elasticsearch search")

        print(doc_list)
        # Step 3: 重排序搜索引擎查询到的结果
        print("\nStep 3: Reorder the results found by search engines...")
        reranked_results = rerank_documents_by_score(user_query, doc_list)
        if not reranked_results:
            raise ValueError("No documents returned after reranking")
        print(reranked_results)
        # # Step 4: 大语言模型根据prompt生成结果
        # print("\nStep 4: Large language model generates results based on prompt...")
        # prompt = generate_llm_response(user_query, reranked_results)
        # print("prompt:\n" + prompt)
        # llm_response = generate_llm_response(tencent_cloud_client, prompt)
        # print("回答结果：" + llm_response['Response']['Choices'][0]['Message']['Content'])

    except Exception as e:
        print(f"\nException occurred while online document query: {e}")
        raise
