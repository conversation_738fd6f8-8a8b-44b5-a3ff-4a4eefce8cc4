# Intellix DS Agent 基础设施层 (infra/) 完整复现指南

## 🏗️ 整体架构概览

`infra/` 目录采用**六边形架构（Hexagonal Architecture）**和**领域驱动设计（DDD）**，是一个完整的数据科学智能体基础设施层。

### 📁 核心模块结构

```
infra/
├── domain/           # 领域层 - 业务实体和端口接口
├── adapter/          # 适配器层 - 数据持久化实现  
├── endpoint/         # 接口层 - API端点和代理管理
├── datascience_agent/# 数据科学智能体核心
├── mcp/             # MCP (Model Context Protocol) 工具集成
├── jupyter/         # Jupyter内核管理
├── memory/          # 记忆和存储管理
├── guardrail/       # 安全防护和内容审核
├── knowledge_base/  # 知识库管理
├── metrics/         # 指标收集和监控
└── server/          # 服务器配置和中间件
```

## 🎯 各模块详细功能

### 1. Domain Layer (领域层)
**职责**: 定义核心业务实体和抽象接口
- **实体（Entity）**: 用户、代理、任务、知识库等业务对象
- **端口（Port）**: 抽象接口，遵循依赖倒置原则
- **基础实体**: 通用实体基类和约束检查

**核心文件**:
- `base_entity.py` - 基础实体类
- `user_info_entity.py` - 用户信息实体
- `agent_list_entity.py` - 代理列表实体
- `knowledge_list_entity.py` - 知识库实体
- `*_port.py` - 各种端口接口定义

### 2. Adapter Layer (适配器层)
**职责**: 实现Domain层端口接口，处理数据持久化
- 数据库访问（MySQL、Redis）
- 外部服务集成
- 数据转换和映射

**核心文件**:
- `user_info_adapter.py` - 用户信息数据访问
- `knowledge_list_adapter.py` - 知识库数据访问
- `task_list_adapter.py` - 任务列表数据访问

### 3. DataScience Agent (核心智能体)
**职责**: 数据科学任务的智能处理和执行
- **Agent Service**: 主服务协调器
- **Graph Orchestrator**: 基于LangGraph的工作流编排
- **Intent Recognizer**: 意图识别
- **Planner**: 任务规划
- **Executor**: 代码执行

**工作流程**:
```
用户输入 → 意图识别 → 任务规划 → 代码执行 → 结果输出
```

### 4. MCP (Model Context Protocol)
**职责**: 工具和外部服务集成
- **AI Search**: 智能搜索
- **Code Generation**: 代码生成
- **NL2SQL**: 自然语言转SQL
- **Jupyter Integration**: Jupyter工具集成

### 5. Jupyter管理
**职责**: Jupyter内核和代码执行环境管理
- 内核生命周期管理
- 代码执行和结果处理
- 会话状态维护

### 6. Memory系统
**职责**: 对话记忆和知识存储
- 对话历史管理
- 语义搜索
- 知识库操作

### 7. Guardrail安全防护
**职责**: 内容安全和合规检查
- 语言检测
- 内容审核
- 安全防护

## 🚀 从零开始复现步骤

### 第一阶段：基础架构搭建

#### 1. 创建目录结构
```bash
mkdir -p infra/{domain,adapter,endpoint,datascience_agent,mcp,jupyter,memory,guardrail,knowledge_base,metrics,server}
```

#### 2. 设置Python环境
```bash
# 使用uv管理虚拟环境
uv init intellix-ds-agent
cd intellix-ds-agent
uv venv .venv --python=3.10
source .venv/bin/activate  # Mac/Linux
```

#### 3. 安装核心依赖
```bash
# 核心框架
uv add fastapi uvicorn
uv add pydantic peewee
uv add langgraph langchain
uv add openai anthropic

# 数据科学相关
uv add jupyter jupyter-client
uv add pandas numpy matplotlib seaborn
uv add sqlalchemy pymysql redis

# 其他工具
uv add ray[default]
uv add elasticsearch
uv add tencentcloud-sdk-python
```

### 第二阶段：Domain层实现

#### 1. 基础实体类
```python
# infra/domain/base_entity.py
class BaseEntity:
    """基础实体类，提供通用功能"""
    
    def __init__(self, *initial_data, **kwargs):
        for dictionary in initial_data:
            for key in dictionary:
                setattr(self, key, dictionary[key])
        for key in kwargs:
            setattr(self, key, kwargs[key])
    
    def check_constraint(self, constraint: dict, allow_missing=False, must=[]):
        """检查实体约束"""
        for key, cons in constraint.items():
            field = self.__dict__.get(key, "")
            if key in must and not field:
                return False
            if allow_missing and not field:
                continue
            if len(field) < cons[0] or len(field) > cons[1]:
                return False
        return True
```

#### 2. 用户实体
```python
# infra/domain/user_info_entity.py
from datetime import datetime
from typing import Optional
from pydantic import BaseModel

class UserInfo(BaseModel):
    """用户信息实体"""
    sub_account_uin: str
    jupyter_host: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        json_encoders = {
            datetime: lambda dt: dt.isoformat()
        }
```

#### 3. 端口接口定义
```python
# infra/domain/user_info_port.py
from abc import ABC, abstractmethod
from typing import Optional
from common.share import context
from .user_info_entity import UserInfo

class UserInfoPort(ABC):
    """用户信息端口接口"""
    
    @abstractmethod
    def get_by_sub_account_uin(self, ctx: context.Context, 
                              sub_account_uin: str) -> Optional[UserInfo]:
        """根据用户ID获取用户信息"""
        pass
    
    @abstractmethod
    def create_or_update(self, ctx: context.Context, 
                        entity: UserInfo) -> bool:
        """创建或更新用户信息"""
        pass
```

### 第三阶段：Adapter层实现

#### 1. 用户信息适配器
```python
# infra/adapter/user_info_adapter.py
from datetime import datetime
from typing import Optional
from common.share import context
from peewee import DoesNotExist
from common.database.database import MysqlPool
from infra.domain.user_info_entity import UserInfo
from infra.domain.user_info_port import UserInfoPort

class UserInfoAdapter(UserInfoPort):
    """用户信息适配器实现"""
    
    def __init__(self, persistence: MysqlPool):
        self.persistence = persistence
    
    def get_by_sub_account_uin(self, ctx: context.Context, 
                              sub_account_uin: str) -> Optional[UserInfo]:
        """根据用户ID获取用户信息"""
        model = self.persistence.models['user_info']
        with self.persistence.pool_db:
            try:
                record = model.select().where(
                    (model.sub_account_uin == sub_account_uin) &
                    (model.deleted_at.is_null())
                ).get()
                
                return UserInfo(
                    sub_account_uin=record.sub_account_uin,
                    jupyter_host=record.jupyter_host,
                    created_at=record.created_at,
                    updated_at=record.updated_at,
                )
            except DoesNotExist:
                return None
    
    def create_or_update(self, ctx: context.Context, 
                        entity: UserInfo) -> bool:
        """创建或更新用户信息"""
        # 实现创建或更新逻辑
        pass
```

### 第四阶段：DataScience Agent核心

#### 1. 状态管理
```python
# infra/datascience_agent/state.py
from typing import Dict, List, Any, Optional, TypedDict

class AgentState(TypedDict):
    """智能体状态定义"""
    current_user_input: Optional[str]
    conversation_history: List[Dict[str, Any]]
    intent_recognizer_slot_state: Dict[str, Any]
    final_summary_content: Optional[str]
    jupyter_events: List[Any]
    database_schema: Optional[Dict[str, Any]]
    ctx: Optional[Any]
    detected_language: Optional[str]
```

#### 2. 图编排器
```python
# infra/datascience_agent/graph_orchestrator.py
from langgraph.graph import StateGraph, END
from .state import AgentState
from .graph_nodes import (
    get_user_input_node,
    intent_recognition_node,
    planner_node,
    executor_node,
    present_output_node
)

def build_graph(llm_client, executor_agent):
    """构建智能体工作流图"""
    workflow = StateGraph(AgentState)
    
    # 添加节点
    workflow.add_node("userInput", get_user_input_node)
    workflow.add_node("intentRecognizer", intent_recognition_node)
    workflow.add_node("planner", planner_node)
    workflow.add_node("executor", executor_node)
    workflow.add_node("presentOutput", present_output_node)
    
    # 设置流程
    workflow.set_entry_point("userInput")
    workflow.add_edge("userInput", "intentRecognizer")
    workflow.add_edge("intentRecognizer", "planner")
    workflow.add_edge("planner", "executor")
    workflow.add_edge("executor", "presentOutput")
    workflow.add_edge("presentOutput", END)
    
    return workflow.compile()
```

### 第五阶段：服务集成

#### 1. Agent服务
```python
# infra/datascience_agent/agent_service.py
from typing import Dict, AsyncGenerator
from .graph_orchestrator import build_graph
from .state import AgentState
from .utils.openai_client import OpenAIClient

class AgentService:
    """智能体主服务"""
    
    def __init__(self, llm_client: OpenAIClient, mcp_manager):
        self.llm_client = llm_client
        self.mcp_manager = mcp_manager
        self.app = build_graph(llm_client, executor_agent)
        self.session_states: Dict[str, AgentState] = {}
    
    async def stream_chat(self, query: str, ctx) -> AsyncGenerator:
        """流式聊天处理"""
        # 实现流式处理逻辑
        pass
```

#### 2. API端点
```python
# infra/endpoint/agent.py
import ray
from fastapi import APIRouter
from common.share.stream_param import StreamGenerationParams

@ray.remote(scheduling_strategy="SPREAD")
class RayAgent:
    """Ray分布式执行模式的Agent实现"""
    
    def __init__(self, ctx, model: str = "", params: StreamGenerationParams = None):
        self.model = model
        self.ctx = ctx
        self.agent_runner = AgentRunner(ctx, model, params)
    
    async def chat(self, params: StreamGenerationParams):
        """执行聊天操作"""
        async for chunk in self.agent_runner.chat(params):
            yield chunk
```

## 📋 实现检查清单

### Domain层 ✅
- [ ] 基础实体类
- [ ] 用户相关实体
- [ ] 代理相关实体  
- [ ] 知识库相关实体
- [ ] 端口接口定义

### Adapter层 ✅
- [ ] 数据库连接配置
- [ ] 用户信息适配器
- [ ] 知识库适配器
- [ ] 任务管理适配器

### DataScience Agent ✅
- [ ] 状态管理
- [ ] 图编排器
- [ ] 意图识别器
- [ ] 任务规划器
- [ ] 代码执行器

### 服务集成 ✅
- [ ] Agent主服务
- [ ] API端点
- [ ] 中间件配置
- [ ] 错误处理

### 外部集成 ✅
- [ ] Jupyter内核管理
- [ ] MCP工具集成
- [ ] 数据库连接
- [ ] 缓存系统

## 🔧 关键技术实现细节

### MCP Manager (工具管理器)
```python
# infra/mcp/manager/mcp_manager.py
class MCPManager:
    """MCP工具管理器 - 管理所有外部工具和服务"""

    def __init__(self, params: StreamGenerationParams = None):
        self.mcp_servers: Dict[str, Dict[str, Any]] = {}
        self.active_sessions: Dict[str, ClientSession] = {}

        # 初始化重放数据收集器
        try:
            from infra.metrics.replay_data_collector import get_replay_collector
            self.replay_collector = get_replay_collector()
        except ImportError:
            self.replay_collector = None

    def register_server(self, server_name: str, server_config: Dict[str, Any]):
        """注册MCP服务器"""
        self.mcp_servers[server_name] = server_config

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]):
        """调用工具"""
        # 实现工具调用逻辑
        pass
```

### Jupyter内核管理
```python
# infra/jupyter/manager.py
class KernelManager:
    """Jupyter内核管理器 - 管理代码执行环境"""

    def __init__(self, use_ray: bool = False):
        self.use_ray = use_ray
        self.active_kernels: Dict[str, KernelClient] = {}
        self.kernel_sessions = ThreadSafeMap()

    async def get_or_create_kernel(self, urn: KernelURN) -> KernelClient:
        """获取或创建内核"""
        if urn.to_str() in self.active_kernels:
            return self.active_kernels[urn.to_str()]

        # 创建新内核
        client = get_kernel_client(urn)
        await client.start_kernel()
        self.active_kernels[urn.to_str()] = client
        return client

    async def execute_code(self, kernel_id: str, code: str):
        """执行代码"""
        client = self.active_kernels.get(kernel_id)
        if not client:
            raise ValueError(f"Kernel {kernel_id} not found")

        return await client.execute(code)
```

### 状态管理系统
```python
# infra/datascience_agent/state.py
class AgentState(TypedDict):
    """智能体状态定义 - 管理整个对话流程的状态"""

    # 基础状态
    stop_requested: Optional[bool]
    current_user_input: Optional[str]
    conversation_history: List[Dict[str, str]]

    # 意图识别状态
    intent_recognizer_slot_state: Optional[Dict[str, Any]]
    identified_intent_name: Optional[str]
    identified_intent_entities: Optional[Dict[str, Any]]

    # 规划和执行状态
    current_plan: Optional[Dict[str, Any]]
    execution_error: Optional[str]

    # 数据源和数据库
    active_dataset_id: Optional[str]
    database_schema: Optional[Dict[str, List[Dict[str, Any]]]]

    # 输出和总结
    final_summary_content: Optional[str]
    final_output_for_user: Optional[str]

    # 系统组件
    mcp_manager: Optional[Any]
    ctx: Optional[Any]
    detected_language: Optional[str]
    jupyter_events: Optional[List[Dict[str, Any]]]

def get_task_category(state: AgentState) -> Optional[str]:
    """从状态中提取任务类别"""
    slot_state = state.get('intent_recognizer_slot_state', {})
    return slot_state.get('content', {}).get('task', {}).get('category', "")
```

## 🔧 配置和部署

### 环境变量配置
```bash
# .env
DATABASE_URL=mysql://user:password@localhost:3306/intellix
REDIS_URL=redis://localhost:6379
OPENAI_API_KEY=your_openai_key
JUPYTER_HOST=localhost:8888
RAY_ADDRESS=ray://localhost:10001
WORKING_DIR=/tmp/ray/
EXECUTION_MODE=ray  # 或 local
```

### Docker配置
```dockerfile
# Dockerfile
FROM python:3.10-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制代码
COPY . .

# 启动服务
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 启动服务
```bash
# 1. 启动Ray集群（分布式模式）
ray start --head --port=6379 --dashboard-host=0.0.0.0

# 2. 启动数据库
docker run -d --name mysql \
  -e MYSQL_ROOT_PASSWORD=password \
  -e MYSQL_DATABASE=intellix \
  -p 3306:3306 mysql:8.0

# 3. 启动Redis
docker run -d --name redis -p 6379:6379 redis:alpine

# 4. 启动Jupyter服务
jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root

# 5. 启动主服务
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## 📚 关键依赖说明

### 核心框架
- **LangGraph**: 工作流编排框架，用于构建复杂的AI工作流
- **Ray**: 分布式计算框架，支持大规模并行处理
- **FastAPI**: 现代Web框架，提供高性能API服务
- **Peewee**: 轻量级ORM框架，简化数据库操作

### AI和机器学习
- **OpenAI/Anthropic**: LLM集成，提供智能对话能力
- **LangChain**: AI应用开发框架
- **Jupyter**: 交互式计算环境

### 数据处理
- **Pandas/NumPy**: 数据分析和科学计算
- **SQLAlchemy**: 数据库抽象层
- **Elasticsearch**: 全文搜索和分析

### 监控和指标
- **OpenTelemetry**: 可观测性框架
- **Prometheus**: 监控和告警
- **Grafana**: 数据可视化

## 🎯 下一步扩展

### 1. 监控系统增强
```python
# infra/metrics/monitoring.py
class MonitoringService:
    """监控服务 - 收集和分析系统指标"""

    def __init__(self):
        self.metrics_collector = MetricsCollector()
        self.alert_manager = AlertManager()

    def track_agent_performance(self, session_id: str, metrics: Dict):
        """跟踪智能体性能"""
        pass

    def detect_anomalies(self, metrics: List[Dict]):
        """异常检测"""
        pass
```

### 2. 安全增强
```python
# infra/security/auth.py
class SecurityManager:
    """安全管理器 - 处理认证、授权和安全防护"""

    def authenticate_user(self, token: str) -> Optional[User]:
        """用户认证"""
        pass

    def authorize_action(self, user: User, action: str, resource: str) -> bool:
        """权限检查"""
        pass

    def sanitize_code(self, code: str) -> str:
        """代码安全检查"""
        pass
```

### 3. 缓存优化
```python
# infra/cache/cache_manager.py
class CacheManager:
    """缓存管理器 - 智能缓存策略"""

    def __init__(self):
        self.redis_client = redis.Redis()
        self.memory_cache = {}

    async def get_or_compute(self, key: str, compute_func: Callable):
        """获取或计算缓存值"""
        pass

    def invalidate_pattern(self, pattern: str):
        """按模式失效缓存"""
        pass
```

### 4. 多模态支持
```python
# infra/multimodal/processor.py
class MultimodalProcessor:
    """多模态处理器 - 支持图像、音频、视频处理"""

    def process_image(self, image_data: bytes) -> Dict:
        """图像处理"""
        pass

    def process_audio(self, audio_data: bytes) -> Dict:
        """音频处理"""
        pass

    def generate_visualization(self, data: Dict) -> bytes:
        """生成可视化"""
        pass
```

### 5. 插件系统
```python
# infra/plugins/plugin_manager.py
class PluginManager:
    """插件管理器 - 支持动态加载和管理插件"""

    def __init__(self):
        self.loaded_plugins: Dict[str, Plugin] = {}

    def load_plugin(self, plugin_path: str) -> Plugin:
        """加载插件"""
        pass

    def register_tool(self, tool: Tool):
        """注册工具"""
        pass

    def execute_plugin(self, plugin_name: str, action: str, params: Dict):
        """执行插件"""
        pass
```

## 🚀 生产环境部署

### Kubernetes配置
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: intellix-ds-agent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: intellix-ds-agent
  template:
    metadata:
      labels:
        app: intellix-ds-agent
    spec:
      containers:
      - name: app
        image: intellix-ds-agent:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
```

### 性能优化建议
1. **数据库连接池**: 使用连接池管理数据库连接
2. **异步处理**: 充分利用异步编程提高并发性能
3. **缓存策略**: 实现多层缓存（内存、Redis、CDN）
4. **负载均衡**: 使用Nginx或云负载均衡器
5. **资源监控**: 实时监控CPU、内存、网络使用情况

这个复现指南提供了完整的架构理解和实现路径，包含了生产环境的考虑。你可以按照这个步骤逐步构建自己的数据科学智能体基础设施。
